<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>钱包注入修复</title>
    <script>
        // 在页面加载前修复ethereum注入问题
        (function() {
            'use strict';
            
            // 检查并修复ethereum属性
            function fixEthereumInjection() {
                try {
                    // 如果ethereum已存在，确保它是可配置的
                    if (window.ethereum && Object.getOwnPropertyDescriptor(window, 'ethereum')) {
                        const descriptor = Object.getOwnPropertyDescriptor(window, 'ethereum');
                        if (!descriptor.configurable) {
                            console.warn('Ethereum property is not configurable, attempting to fix...');
                            
                            // 尝试删除并重新定义
                            try {
                                delete window.ethereum;
                            } catch (e) {
                                console.warn('Cannot delete ethereum property:', e);
                            }
                        }
                    }
                } catch (e) {
                    console.warn('Error checking ethereum property:', e);
                }
            }
            
            // 在DOM加载完成后执行修复
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', fixEthereumInjection);
            } else {
                fixEthereumInjection();
            }
            
            // 监听钱包注入事件
            window.addEventListener('ethereum#initialized', function() {
                console.log('Ethereum provider initialized');
            });
            
        })();
    </script>
</head>
<body>
    <!-- 页面内容 -->
</body>
</html> 