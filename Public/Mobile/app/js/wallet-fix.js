// 钱包注入修复脚本
(function() {
    'use strict';
    
    // 检查ethereum是否已存在且不可配置
    function isEthereumDefined() {
        try {
            return window.ethereum !== undefined && 
                   Object.getOwnPropertyDescriptor(window, 'ethereum') !== undefined;
        } catch (e) {
            return false;
        }
    }
    
    // 安全注入ethereum对象
    function safeInjectEthereum(ethereumProvider) {
        if (isEthereumDefined()) {
            console.warn('Ethereum provider already exists, skipping injection');
            return false;
        }
        
        try {
            Object.defineProperty(window, 'ethereum', {
                value: ethereumProvider,
                writable: true,
                configurable: true,
                enumerable: true
            });
            return true;
        } catch (e) {
            console.error('Failed to inject ethereum provider:', e);
            return false;
        }
    }
    
    // 重写evmAsk.js的注入逻辑
    if (typeof window !== 'undefined') {
        // 保存原始的Object.defineProperty
        const originalDefineProperty = Object.defineProperty;
        
        // 重写Object.defineProperty以处理ethereum属性
        Object.defineProperty = function(obj, prop, descriptor) {
            if (obj === window && prop === 'ethereum') {
                if (isEthereumDefined()) {
                    console.warn('Preventing ethereum property redefinition');
                    return obj; // 返回原对象，不执行重定义
                }
            }
            return originalDefineProperty.call(this, obj, prop, descriptor);
        };
    }
    
    // 导出安全注入函数
    window.safeInjectEthereum = safeInjectEthereum;
    
})(); 