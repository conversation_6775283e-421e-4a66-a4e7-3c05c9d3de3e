(window.webpackJsonp=window.webpackJsonp||[]).push([[14],{193:function(e,t,n){"use strict";var r=n(135),a=n.n(r),l=n(194),s=n.n(l),i=n(195),u=n.n(i),o=n(196),c=n.n(o),d=n(59);function f(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"===typeof e)return p(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return p(e,t)}(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var l,s=!0,i=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return s=e.done,e},e:function(e){i=!0,l=e},f:function(){try{s||null==n.return||n.return()}finally{if(i)throw l}}}}function p(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var v={onboardingStep:"start",entity:"individual",userType:"selfDirected"};var E={modalType:null,modalProps:{}};var m={entities:{},posts:{},postsForEntity:{},links:{},linksForEntity:{}};var S=Object(d.b)({user:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:v,t=arguments.length>1?arguments[1]:void 0,n=s()({},e);switch(t.type){case"UPDATE_USER":return c()(n,t.path,t.value);case"MERGE_USER":return u()(n,t.user);case"LOGOUT_USER":return v;case"LOAD_USER":return t.user;default:return e}},investments:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"LOAD_INVESTMENTS":return t.investments;case"LOGOUT_USER":return null;default:return e}},subscribees:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"LOAD_SUBSCRIBEES":return t.subscribees;case"LOGOUT_USER":return null;default:return e}},clients:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"LOAD_CLIENTS":return t.clients;case"LOGOUT_USER":return null;default:return e}},clientApplications:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"LOAD_CLIENT_APPLICATIONS":return t.clientApplications;case"LOGOUT_USER":return null;default:return e}},selectedUserId:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"SET_SELECTED_USER_ID":return t.selectedUserId;case"LOGOUT_USER":return null;default:return e}},selectedInvestmentId:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"SET_SELECTED_INVESTMENT_ID":return t.selectedInvestmentId;case"LOGOUT_USER":return null;default:return e}},alertMessage:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"SET_MESSAGE":return t.message;case"REMOVE_MESSAGE":return null;default:return e}},modal:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:E,t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"SHOW_MODAL":return{modalType:t.modalType,modalProps:t.modalProps};case"HIDE_MODAL":return E;default:return e}},resources:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:m,t=arguments.length>1?arguments[1]:void 0,n=a.a.assign({},e);switch(t.type){case"SET_FEATURED_POST":return n.featuredPost=t.featuredPost,n;case"SET_POPULAR":return n.popular=t.popular,n;case"SET_HOME_PAGE_RESOURCES":return n.homePageResources=t.homePageResources,n;case"SET_ADVISOR_LETTERS":return n.advisorLetters=t.advisorLetters,n;case"SET_POSTS_FOR_ENTITY":n.entities[t.slug]=t.entity;var r,l=f(t.posts);try{for(l.s();!(r=l.n()).done;){var s=r.value;n.posts[s.fields.slug]||(n.posts[s.fields.slug]=s)}}catch(c){l.e(c)}finally{l.f()}return n.postsForEntity[t.slug]=t.posts,n;case"SET_ALL_POSTS":var i,u=f(t.posts);try{for(u.s();!(i=u.n()).done;){var o=i.value;n.posts[o.fields.slug]||(n.posts[o.fields.slug]=o)}}catch(c){u.e(c)}finally{u.f()}return n;default:return e}}});t.a=S},197:function(e,t,n){"use strict";var r=n(70),a=n(71),l=n(73),s=n(72),i=n(74),u=n(10),o=n(1),c=function(e){function t(){return Object(r.a)(this,t),Object(l.a)(this,Object(s.a)(t).apply(this,arguments))}return Object(i.a)(t,e),Object(a.a)(t,[{key:"componentDidUpdate",value:function(e){this.props.location!==e.location&&window.scrollTo(0,0)}},{key:"render",value:function(){return this.props.children}}]),t}(o.Component);t.a=Object(u.f)(c)},198:function(e,t,n){"use strict";var r=n(70),a=n(71),l=n(73),s=n(72),i=n(74),u=n(142),o=n.n(u),c=n(1),d=n.n(c),f=n(75),p=n(10),v=Object(c.lazy)(function(){return Promise.all([n.e(3),n.e(4),n.e(5),n.e(1),n.e(24)]).then(n.bind(null,1271))}),E=Object(c.lazy)(function(){return Promise.all([n.e(2),n.e(3),n.e(26)]).then(n.bind(null,1273))}),m=Object(c.lazy)(function(){return Promise.all([n.e(3),n.e(22),n.e(27)]).then(n.bind(null,1269))}),S=Object(c.lazy)(function(){return Promise.all([n.e(2),n.e(3),n.e(4),n.e(5),n.e(17)]).then(n.bind(null,1275))}),h=Object(c.lazy)(function(){return Promise.all([n.e(2),n.e(3),n.e(7),n.e(8),n.e(23)]).then(n.bind(null,1272))}),O=function(e){var t=e.site;switch(void 0===t?"www.bitwiseinvestments.com":t){case"www.bitqetf.com":return d.a.createElement(c.Suspense,{fallback:d.a.createElement("div",null)},d.a.createElement(v,null));case"www.bitcointradevolume.com":return d.a.createElement(c.Suspense,{fallback:d.a.createElement("div",null)},d.a.createElement(E,null));case"www.digitalassetindexfund.com":return d.a.createElement(c.Suspense,{fallback:d.a.createElement("div",null)},d.a.createElement(h,null));case"app.bitwiseinvestments.com":return d.a.createElement(c.Suspense,{fallback:d.a.createElement("div",null)},d.a.createElement(S,null));default:return d.a.createElement(c.Suspense,{fallback:d.a.createElement("div",null)},d.a.createElement(m,null))}},y=function(e){function t(){return Object(r.a)(this,t),Object(l.a)(this,Object(s.a)(t).apply(this,arguments))}return Object(i.a)(t,e),Object(a.a)(t,[{key:"componentDidMount",value:function(){var e=localStorage&&localStorage.getItem("user"),t=localStorage&&localStorage.getItem("investments");e&&this.props.dispatch({type:"LOAD_USER",user:JSON.parse(e)}),t&&this.props.dispatch({type:"LOAD_INVESTMENTS",investments:JSON.parse(t)});var n=o.a.parse(this.props.location.search);n&&n.gclid&&this.props.dispatch({type:"UPDATE_USER",path:"gclid",value:n.gclid})}},{key:"render",value:function(){return d.a.createElement("div",{style:{color:"#222222",fontFamily:"NeueHaasUnicaPro",fontSize:16,fontWeight:400}},d.a.createElement(O,{site:"app.bitwiseinvestments.com"}))}}]),t}(c.Component);t.a=Object(p.f)(Object(f.b)()(y))},244:function(e,t,n){e.exports=n(245)},245:function(e,t,n){"use strict";n.r(t),function(e){var t=n(1),r=n.n(t),a=n(136),l=n.n(a),s=n(75),i=n(76),u=n(59),o=n(198),c=(n(266),n(193)),d=n(197);e._babelPolyfill||n(324);var f=Object(u.c)(c.a);f.subscribe(function(){var e=f.getState().user,t=f.getState().investments;localStorage.setItem("user",JSON.stringify(e)),localStorage.setItem("investments",JSON.stringify(t))});var p;p=o.a,l.a.render(r.a.createElement(s.a,{store:f},r.a.createElement(i.a,null,r.a.createElement(d.a,null,r.a.createElement(p,null)))),document.getElementById("root"))}.call(this,n(53))},266:function(e,t,n){}},[[244,15,16]]]);
//# sourceMappingURL=main.7e2c1548.chunk.js.map