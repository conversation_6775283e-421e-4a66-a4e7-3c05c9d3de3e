const messages = {
    en: {
        message: {
            title: 'Has been serving',
            title1: 'Global cryptocurrency leader',
            placeholder: "Email or mobile number",
            register: 'Register',
            hour: '24-hour trading volume',
            pc: 'Computer',
            phoneR: 'Registration of mobile phone number',
            password: 'password',
            password1: 'confirm password',
            code: 'Invitation Code (required)',
            Ido: 'I agree with',
            xy: '《User Agreement》',
            isRegister: 'The registration',
            phoneYZ: 'Mobile phone Number Verification',
            OkYZ: 'Please complete the verification as required',
            sendCode: 'Verification code sent to',
            phoneCode: 'Mobile verification code',
            sendCode1: 'Send verification code',
            smitYZ: 'Submit the validation'
        }
    },
    zh: {
        message: {
            title: '一直以来担任',
            title1: '全球加密货币领导者',
            placeholder: "电子邮箱或手机号",
            register: '注册',
            hour: '24小时交易量',
            pc: '电脑',
            phoneR: '手机号注册',
            password: '登陆密码',
            password1: '确认密码',
            code: '邀请码(必填)',
            Ido: '我同意',
            xy: '《用户协议》',
            isRegister: '提交注册',
            phoneYZ: '手机号验证',
            OkYZ: '请按要求完成验证',
            sendCode: '验证码发送至',
            phoneCode: '手机验证码',
            sendCode1: '发送验证码',
            smitYZ: '提交验证'
        }
    }
}
