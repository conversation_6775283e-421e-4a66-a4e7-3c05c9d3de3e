(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[586],{8887:function(e,t,n){"use strict";n.d(t,{f:function(){return a}});var i=n(2130),r=n(2784),o=n(7896);const a=r.forwardRef(((e,t)=>{const{ratio:n=1,style:a,...c}=e;return r.createElement("div",{style:{position:"relative",width:"100%",paddingBottom:100/n+"%"},"data-radix-aspect-ratio-wrapper":""},r.createElement(i.W.div,(0,o.Z)({},c,{ref:t,style:{...a,position:"absolute",top:0,right:0,bottom:0,left:0}})))}))},3421:function(e,t){"use strict";t.Q=function(e,t){if("string"!==typeof e)throw new TypeError("argument str must be a string");for(var i={},r=t||{},a=e.split(";"),c=r.decode||n,s=0;s<a.length;s++){var u=a[s],l=u.indexOf("=");if(!(l<0)){var d=u.substring(0,l).trim();if(void 0==i[d]){var f=u.substring(l+1,u.length).trim();'"'===f[0]&&(f=f.slice(1,-1)),i[d]=o(f,c)}}}return i},t.q=function(e,t,n){var o=n||{},a=o.encode||i;if("function"!==typeof a)throw new TypeError("option encode is invalid");if(!r.test(e))throw new TypeError("argument name is invalid");var c=a(t);if(c&&!r.test(c))throw new TypeError("argument val is invalid");var s=e+"="+c;if(null!=o.maxAge){var u=o.maxAge-0;if(isNaN(u)||!isFinite(u))throw new TypeError("option maxAge is invalid");s+="; Max-Age="+Math.floor(u)}if(o.domain){if(!r.test(o.domain))throw new TypeError("option domain is invalid");s+="; Domain="+o.domain}if(o.path){if(!r.test(o.path))throw new TypeError("option path is invalid");s+="; Path="+o.path}if(o.expires){if("function"!==typeof o.expires.toUTCString)throw new TypeError("option expires is invalid");s+="; Expires="+o.expires.toUTCString()}o.httpOnly&&(s+="; HttpOnly");o.secure&&(s+="; Secure");if(o.sameSite){switch("string"===typeof o.sameSite?o.sameSite.toLowerCase():o.sameSite){case!0:s+="; SameSite=Strict";break;case"lax":s+="; SameSite=Lax";break;case"strict":s+="; SameSite=Strict";break;case"none":s+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}}return s};var n=decodeURIComponent,i=encodeURIComponent,r=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/;function o(e,t){try{return t(e)}catch(n){return e}}},6239:function(e,t,n){"use strict";function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}function r(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function o(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var i,r,o=[],a=!0,c=!1;try{for(n=n.call(e);!(a=(i=n.next()).done)&&(o.push(i.value),!t||o.length!==t);a=!0);}catch(s){c=!0,r=s}finally{try{a||null==n.return||n.return()}finally{if(c)throw r}}return o}}(e,t)||c(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function a(e){return function(e){if(Array.isArray(e))return i(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||c(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(e,t){if(e){if("string"===typeof e)return i(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?i(e,t):void 0}}t.default=function(e){var t=e.src,n=e.sizes,i=e.unoptimized,c=void 0!==i&&i,s=e.priority,u=void 0!==s&&s,g=e.loading,m=e.lazyRoot,O=void 0===m?null:m,j=e.lazyBoundary,C=void 0===j?"200px":j,I=e.className,_=e.quality,L=e.width,P=e.height,R=e.style,N=e.objectFit,T=e.objectPosition,q=e.onLoadingComplete,M=e.loader,D=void 0===M?k:M,U=e.placeholder,H=void 0===U?"empty":U,W=e.blurDataURL,B=v(e,["src","sizes","unoptimized","priority","loading","lazyRoot","lazyBoundary","className","quality","width","height","style","objectFit","objectPosition","onLoadingComplete","loader","placeholder","blurDataURL"]),F=l.useContext(h.ImageConfigContext),K=l.useMemo((function(){var e=b||F||f.imageConfigDefault,t=a(e.deviceSizes).concat(a(e.imageSizes)).sort((function(e,t){return e-t})),n=e.deviceSizes.sort((function(e,t){return e-t}));return y({},e,{allSizes:t,deviceSizes:n})}),[F]),V=B,J=n?"responsive":"intrinsic";"layout"in V&&(V.layout&&(J=V.layout),delete V.layout);var Q="";if(function(e){return"object"===typeof e&&(A(e)||function(e){return void 0!==e.src}(e))}(t)){var G=A(t)?t.default:t;if(!G.src)throw new Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received ".concat(JSON.stringify(G)));if(W=W||G.blurDataURL,Q=G.src,(!J||"fill"!==J)&&(P=P||G.height,L=L||G.width,!G.height||!G.width))throw new Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received ".concat(JSON.stringify(G)))}t="string"===typeof t?t:Q;var Z=x(L),$=x(P),X=x(_),Y=!u&&("lazy"===g||"undefined"===typeof g);(t.startsWith("data:")||t.startsWith("blob:"))&&(c=!0,Y=!1);w.has(t)&&(Y=!1);var ee,te=o(p.useIntersection({rootRef:O,rootMargin:C,disabled:!Y}),2),ne=te[0],ie=te[1],re=!Y||ie,oe={boxSizing:"border-box",display:"block",overflow:"hidden",width:"initial",height:"initial",background:"none",opacity:1,border:0,margin:0,padding:0},ae={boxSizing:"border-box",display:"block",width:"initial",height:"initial",background:"none",opacity:1,border:0,margin:0,padding:0},ce=!1,se={position:"absolute",top:0,left:0,bottom:0,right:0,boxSizing:"border-box",padding:0,border:"none",margin:"auto",display:"block",width:0,height:0,minWidth:"100%",maxWidth:"100%",minHeight:"100%",maxHeight:"100%",objectFit:N,objectPosition:T};0;0;var ue=Object.assign({},R,"raw"===J?{aspectRatio:"".concat(Z," / ").concat($)}:se),le="blur"===H?{filter:"blur(20px)",backgroundSize:N||"cover",backgroundImage:'url("'.concat(W,'")'),backgroundPosition:T||"0% 0%"}:{};if("fill"===J)oe.display="block",oe.position="absolute",oe.top=0,oe.left=0,oe.bottom=0,oe.right=0;else if("undefined"!==typeof Z&&"undefined"!==typeof $){var de=$/Z,fe=isNaN(de)?"100%":"".concat(100*de,"%");"responsive"===J?(oe.display="block",oe.position="relative",ce=!0,ae.paddingTop=fe):"intrinsic"===J?(oe.display="inline-block",oe.position="relative",oe.maxWidth="100%",ce=!0,ae.maxWidth="100%",ee="data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%27".concat(Z,"%27%20height=%27").concat($,"%27/%3e")):"fixed"===J&&(oe.display="inline-block",oe.position="relative",oe.width=Z,oe.height=$)}else 0;var pe={src:S,srcSet:void 0,sizes:void 0};re&&(pe=E({config:K,src:t,unoptimized:c,layout:J,width:Z,quality:X,sizes:n,loader:D}));var he=t;0;var ge;0;var me=(r(ge={},"imagesrcset",pe.srcSet),r(ge,"imagesizes",pe.sizes),ge),ye=l.default.useLayoutEffect,ve=l.useRef(q),be=l.useRef(null);l.useEffect((function(){ve.current=q}),[q]),ye((function(){ne(be.current)}),[ne]),l.useEffect((function(){!function(e,t,n,i,r){var o=function(){var n=e.current;n&&(n.src!==S&&("decode"in n?n.decode():Promise.resolve()).catch((function(){})).then((function(){if(e.current&&(w.add(t),"blur"===i&&(n.style.filter="",n.style.backgroundSize="",n.style.backgroundImage="",n.style.backgroundPosition=""),r.current)){var o=n.naturalWidth,a=n.naturalHeight;r.current({naturalWidth:o,naturalHeight:a})}})))};e.current&&(e.current.complete?o():e.current.onload=o)}(be,he,0,H,ve)}),[he,J,H,re]);var we=y({isLazy:Y,imgAttributes:pe,heightInt:$,widthInt:Z,qualityInt:X,layout:J,className:I,imgStyle:ue,blurStyle:le,imgRef:be,loading:g,config:K,unoptimized:c,placeholder:H,loader:D,srcString:he},V);return l.default.createElement(l.default.Fragment,null,"raw"===J?l.default.createElement(z,Object.assign({},we)):l.default.createElement("span",{style:oe},ce?l.default.createElement("span",{style:ae},ee?l.default.createElement("img",{style:{display:"block",maxWidth:"100%",width:"initial",height:"initial",background:"none",opacity:1,border:0,margin:0,padding:0},alt:"","aria-hidden":!0,src:ee}):null):null,l.default.createElement(z,Object.assign({},we))),u?l.default.createElement(d.default,null,l.default.createElement("link",Object.assign({key:"__nimg-"+pe.src+pe.srcSet+pe.sizes,rel:"preload",as:"image",href:pe.srcSet?void 0:pe.src},me))):null)};var s,u,l=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){var i=Object.defineProperty&&Object.getOwnPropertyDescriptor?Object.getOwnPropertyDescriptor(e,n):{};i.get||i.set?Object.defineProperty(t,n,i):t[n]=e[n]}return t.default=e,t}(n(2784)),d=(s=n(7016))&&s.__esModule?s:{default:s},f=n(5515),p=n(8599),h=n(3581),g=(n(8993),n(6998));function m(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function y(e){for(var t=arguments,n=function(n){var i=null!=t[n]?t[n]:{},r=Object.keys(i);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(i).filter((function(e){return Object.getOwnPropertyDescriptor(i,e).enumerable})))),r.forEach((function(t){m(e,t,i[t])}))},i=1;i<arguments.length;i++)n(i);return e}function v(e,t){if(null==e)return{};var n,i,r=function(e,t){if(null==e)return{};var n,i,r={},o=Object.keys(e);for(i=0;i<o.length;i++)n=o[i],t.indexOf(n)>=0||(r[n]=e[n]);return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(i=0;i<o.length;i++)n=o[i],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}u={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",experimentalLayoutRaw:!1};var b={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",experimentalLayoutRaw:!1},w=new Set,S=(new Map,"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7");var O=new Map([["default",function(e){var t=e.config,n=e.src,i=e.width,r=e.quality;0;if(n.endsWith(".svg")&&!t.dangerouslyAllowSVG)return n;return"".concat(g.normalizePathTrailingSlash(t.path),"?url=").concat(encodeURIComponent(n),"&w=").concat(i,"&q=").concat(r||75)}],["imgix",function(e){var t=e.config,n=e.src,i=e.width,r=e.quality,o=new URL("".concat(t.path).concat(j(n))),a=o.searchParams;a.set("auto",a.get("auto")||"format"),a.set("fit",a.get("fit")||"max"),a.set("w",a.get("w")||i.toString()),r&&a.set("q",r.toString());return o.href}],["cloudinary",function(e){var t=e.config,n=e.src,i=e.width,r=e.quality,o=["f_auto","c_limit","w_"+i,"q_"+(r||"auto")].join(",")+"/";return"".concat(t.path).concat(o).concat(j(n))}],["akamai",function(e){var t=e.config,n=e.src,i=e.width;return"".concat(t.path).concat(j(n),"?imwidth=").concat(i)}],["custom",function(e){var t=e.src;throw new Error('Image with src "'.concat(t,'" is missing "loader" prop.')+"\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader")}]]);function A(e){return void 0!==e.default}function E(e){var t=e.config,n=e.src,i=e.unoptimized,r=e.layout,o=e.width,c=e.quality,s=e.sizes,u=e.loader;if(i)return{src:n,srcSet:void 0,sizes:void 0};var l=function(e,t,n,i){var r=e.deviceSizes,o=e.allSizes;if(i&&("fill"===n||"responsive"===n||"raw"===n)){for(var c,s=/(^|\s)(1?\d?\d)vw/g,u=[];c=s.exec(i);c)u.push(parseInt(c[2]));if(u.length){var l,d=.01*(l=Math).min.apply(l,a(u));return{widths:o.filter((function(e){return e>=r[0]*d})),kind:"w"}}return{widths:o,kind:"w"}}return"number"!==typeof t||"fill"===n||"responsive"===n?{widths:r,kind:"w"}:{widths:a(new Set([t,2*t].map((function(e){return o.find((function(t){return t>=e}))||o[o.length-1]})))),kind:"x"}}(t,o,r,s),d=l.widths,f=l.kind,p=d.length-1;return{sizes:s||"w"!==f?s:"100vw",srcSet:d.map((function(e,i){return"".concat(u({config:t,src:n,quality:c,width:e})," ").concat("w"===f?e:i+1).concat(f)})).join(", "),src:u({config:t,src:n,quality:c,width:d[p]})}}function x(e){return"number"===typeof e?e:"string"===typeof e?parseInt(e,10):void 0}function k(e){var t,n=(null===(t=e.config)||void 0===t?void 0:t.loader)||"default",i=O.get(n);if(i)return i(e);throw new Error('Unknown "loader" found in "next.config.js". Expected: '.concat(f.VALID_LOADERS.join(", "),". Received: ").concat(n))}var z=function(e){var t=e.imgAttributes,n=e.heightInt,i=e.widthInt,r=e.qualityInt,o=e.layout,a=e.className,c=e.imgStyle,s=e.blurStyle,u=e.isLazy,d=e.imgRef,f=e.placeholder,p=e.loading,h=e.sizes,g=e.srcString,m=e.config,b=e.unoptimized,w=e.loader,S=v(e,["imgAttributes","heightInt","widthInt","qualityInt","layout","className","imgStyle","blurStyle","isLazy","imgRef","placeholder","loading","sizes","srcString","config","unoptimized","loader"]);return l.default.createElement(l.default.Fragment,null,l.default.createElement("img",Object.assign({},S,t,"raw"!==o||h?{}:{height:n,width:i},{decoding:"async","data-nimg":o,className:a,ref:d,style:y({},c,s)})),(u||"blur"===f)&&l.default.createElement("noscript",null,l.default.createElement("img",Object.assign({},S,E({config:m,src:g,unoptimized:b,layout:o,width:i,quality:r,sizes:h,loader:w}),"raw"!==o||h?{}:{height:n,width:i},{decoding:"async","data-nimg":o,style:c,className:a,loading:p||"lazy"}))))};function j(e){return"/"===e[0]?e.slice(1):e}},7729:function(e,t,n){e.exports=n(7016)},6577:function(e,t,n){e.exports=n(6239)},9257:function(e,t,n){"use strict";n.d(t,{Z:function(){return l}});var i=n(2784),r=n(3421);function o(e,t){void 0===t&&(t={});var n=function(e){if(e&&"j"===e[0]&&":"===e[1])return e.substr(2);return e}(e);if(function(e,t){return"undefined"===typeof t&&(t=!e||"{"!==e[0]&&"["!==e[0]&&'"'!==e[0]),!t}(n,t.doNotParse))try{return JSON.parse(n)}catch(i){}return e}var a=function(){return a=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},a.apply(this,arguments)},c=function(){function e(e,t){var n=this;this.changeListeners=[],this.HAS_DOCUMENT_COOKIE=!1,this.cookies=function(e,t){return"string"===typeof e?r.Q(e,t):"object"===typeof e&&null!==e?e:{}}(e,t),new Promise((function(){n.HAS_DOCUMENT_COOKIE="object"===typeof document&&"string"===typeof document.cookie})).catch((function(){}))}return e.prototype._updateBrowserValues=function(e){this.HAS_DOCUMENT_COOKIE&&(this.cookies=r.Q(document.cookie,e))},e.prototype._emitChange=function(e){for(var t=0;t<this.changeListeners.length;++t)this.changeListeners[t](e)},e.prototype.get=function(e,t,n){return void 0===t&&(t={}),this._updateBrowserValues(n),o(this.cookies[e],t)},e.prototype.getAll=function(e,t){void 0===e&&(e={}),this._updateBrowserValues(t);var n={};for(var i in this.cookies)n[i]=o(this.cookies[i],e);return n},e.prototype.set=function(e,t,n){var i;"object"===typeof t&&(t=JSON.stringify(t)),this.cookies=a(a({},this.cookies),((i={})[e]=t,i)),this.HAS_DOCUMENT_COOKIE&&(document.cookie=r.q(e,t,n)),this._emitChange({name:e,value:t,options:n})},e.prototype.remove=function(e,t){var n=t=a(a({},t),{expires:new Date(1970,1,1,0,0,1),maxAge:0});this.cookies=a({},this.cookies),delete this.cookies[e],this.HAS_DOCUMENT_COOKIE&&(document.cookie=r.q(e,"",n)),this._emitChange({name:e,value:void 0,options:t})},e.prototype.addChangeListener=function(e){this.changeListeners.push(e)},e.prototype.removeChangeListener=function(e){var t=this.changeListeners.indexOf(e);t>=0&&this.changeListeners.splice(t,1)},e}(),s=i.createContext(new c),u=(s.Provider,s.Consumer,s);function l(e){var t=(0,i.useContext)(u);if(!t)throw new Error("Missing <CookiesProvider>");var n=t.getAll(),r=(0,i.useState)(n),o=r[0],a=r[1],c=(0,i.useRef)(o);return"undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement&&(0,i.useLayoutEffect)((function(){function n(){var n=t.getAll();(function(e,t,n){if(!e)return!0;for(var i=0,r=e;i<r.length;i++){var o=r[i];if(t[o]!==n[o])return!0}return!1})(e||null,n,c.current)&&a(n),c.current=n}return t.addChangeListener(n),function(){t.removeChangeListener(n)}}),[t]),[o,(0,i.useMemo)((function(){return t.set.bind(t)}),[t]),(0,i.useMemo)((function(){return t.remove.bind(t)}),[t])]}},3703:function(e,t,n){"use strict";n.d(t,{y:function(){return o}});var i=n(2784),r=function(){return r=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},r.apply(this,arguments)};function o(e){return e.map((function(e){var t=e.tag,n=e.attributes,o=e.content,a=[t];n&&"property"in n&&a.push(n.property),n&&"name"in n&&a.push(n.name),n&&"rel"in n&&a.push(n.rel),n&&"sizes"in n&&a.push(n.sizes);var c=t;return i.createElement(c,r({key:a.join("-")},n),o)}))}}}]);