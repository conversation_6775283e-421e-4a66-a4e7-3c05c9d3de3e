/*---------------------------------------------头部CSS-------------------------------------------------*/

.mywallet {
    display: inline-block;
    *display: inline;
    *zoom: 1;
    font-size: 12px;
    _width: 300px;
}

.mywallet dt {
    float: left;
    line-height: 32px;
    position: relative;
    z-index: 110;
}

.mywallet_name {
    padding: 0 11px;
    border: 0px solid #f6f6f6;
    border-bottom: none;
    position: relative;
    z-index: 120;
}

.mywallet dt:hover .mywallet_name {
    background: #ffffff;
    box-shadow: 0 -4px 4px #d9d9d9;
    border: 0px solid #d9d9d9;
    border-bottom: none;
}

.mywallet_list {
    position: absolute;
    right: 0;
    top: 32px;
    background: #ffffff;
    box-shadow: 0 0 4px #d9d9d9;
    border: 1px solid #d9d9d9;
    padding: 9px 14px 8px;
    width: 406px;
    display: none;
}

.mywallet_list .clear {
    width: 100%;
    height: auto;
    overflow: hidden;
    clear: both;
}

.mywallet_list ul {
    float: left;
    width: 203px;
}

.mywallet_list h4 {
    font-weight: normal;
    margin-bottom: 4px;
    height: 30px;
    line-height: 30px;
    font-size: 14px;
    color: #333333;
    float: left;
    margin-right: 10px;
}

.mywallet_list li {
    height: 30px;
    line-height: 30px;
    font-size: 12px;
}

.mywallet_list li em,
.price_today_ul em,
#profit_list em {
    float: left;
    width: 26px;
    height: 19px;
    margin-top: 11px;
    background: url(../images/coinstyle.png?v=1.39) no-repeat;
}

.mywallet_list li strong {
    float: left;
    font-weight: normal;
    color: #333333;
}

.mywallet_list span {
    float: left;
}

.balance_list li a span {
    color: #669900;
}

.freeze_list li a span {
    color: #73bee4;
}

.mywallet dt a {
    color: #73bee4;
    float: left;
}

.mywallet dt a:hover strong {
    color: #73bee4;
}

.mywallet dt i {
    height: 3px;
    margin-top: 15px;
    margin-left: 6px;
    width: 6px;
    overflow: hidden;
    background: url(../images/icon.png) no-repeat left -181px;
    float: left;
    webkit-transition: 0.3s ease all;
    transition: 0.3s ease all;
    -o-transition: 0.3s ease all;
    -moz-transition: 0.3s ease all;
    -ms-transition: 0.3s ease all;
    transform: rotateZ(0deg);
    -webkit-transform: rotateZ(0deg);
    -moz-transform: rotateZ(0deg);
    -ms-transform: rotateZ(0deg);
    -o-transform: rotateZ(0deg);
    display: inline;
}

.mywallet dt:hover i {
    transform: rotateZ(180deg);
    -webkit-transform: rotateZ(180deg);
    -moz-transform: rotateZ(180deg);
    -ms-transform: rotateZ(180deg);
    -o-transform: rotateZ(180deg);
}

.mywallet dd {
    float: left;
    padding: 0 6px 0 5px;
    line-height: 33px;
    color: #333333;
}

.mywallet dd span,
.mywallet dd a {
    color: #73bee4;
}

.mywallet_btn_box {
    border-top: 1px dotted #888888;
    padding-top: 15px;
    width: 408px;
}

.mywallet_btn_box222 {
    width: 408px;
}

.mywallet_btn_box a {
    float: left;
    width: 55px;
    height: 25px;
    border: 1px solid #73bee4;
    margin-right: 10px;
    margin-bottom: 10px;
    color: #73bee4;
    background: #ffffff;
    line-height: 25px;
    font-size: 12px;
    text-align: center;
    border-radius: 3px;
    webkit-transition: 0.3s ease all;
    transition: 0.3s ease all;
    -o-transition: 0.3s ease all;
    -moz-transition: 0.3s ease all;
    -ms-transition: 0.3s ease all;
}

.mywallet_btn_box222 a {
    float: left;
    width: 55px;
    height: 25px;
    border: 1px solid #73bee4;
    margin-right: 10px;
    margin-bottom: 10px;
    color: #73bee4;
    background: #ffffff;
    line-height: 25px;
    font-size: 12px;
    text-align: center;
    border-radius: 3px;
    webkit-transition: 0.3s ease all;
    transition: 0.3s ease all;
    -o-transition: 0.3s ease all;
    -moz-transition: 0.3s ease all;
    -ms-transition: 0.3s ease all;
}

.mywallet_btn_box a:hover {
    color: #ffffff;
    background: #73bee4;
    text-decoration: none;
}

.mywallet_btn_box222 a:hover {
    color: #ffffff;
    background: #73bee4;
    text-decoration: none;
}

.unlogin {
    border: solid 1px #73bee4;
    border-radius: 3px;
    height: 19px;
    line-height: 19px;
    margin-top: 6px;
    font-size: 12px;
    margin-left: 412px;
}

.unlogin a {
    display: inline-block;
    padding: 0 8px;
}

.unlogin a:first-child {
    border-right: solid 1px #73bee4;
}

.unlogin a:hover {
    background: #73bee4;
    text-decoration: none;
    color: #fff !important;
}

.qqkefu:hover {
    background: url(../images/qqkefu.png) no-repeat 0px -33px;
    margin-top: 4px;
}

.qqkefu {
    display: inline-block;
    width: 135px;
    height: 30px;
    background: url(../images/qqkefu.png) no-repeat 0px 0px;
    margin-top: 4px;
}

.iphone {
    display: inline-block;
    width: 130px;
    height: 30px;
    font-size: 20px;
    font-family: tahoma, Microsoft YaHei, Arial, Helvetica, sans-serif;
    color: #73bee4;
}

.zhuce {
    background: url(../images/zhuce.png) no-repeat 6px 6px;
    display: inline-block;
    padding-left: 29px;
    background-size: 35%;
}

.layui-btn{background-color: #73bee4 !important;}