html, body {
  margin: 0;
}

body {
  background: #000;
  color: #fff;
  font-family: sans-serif;
  overflow: hidden;
}

div.starfield {
  position: fixed;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
div.starfield .static {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 3px;
  height: 3px;
  border-radius: 100%;
  transform-origin: -31vw -38vh;
  box-shadow: 36vw -46vh 1px 0.75px rgba(255, 244, 245, 0.5235723442), -68vw -54vh 1px 0.75px rgba(252, 243, 255, 0.9688846036), 27vw -66vh 1px 0.75px #f3f6f1, 21vw -38vh 1px 0.75px rgba(241, 249, 243, 0.8680567471), -15vw -3vh 1px 0.75px rgba(252, 244, 250, 0.8741643268), -95vw -40vh 1px 0.75px #f1f4ff, -88vw 14vh 1px 0.75px #fcf3fb, -83vw 12vh 1px 0.75px #f6f8fe, -61vw 39vh 1px 0.75px #f7f2ff, 41vw 34vh 1px 0.75px rgba(246, 247, 248, 0.9318846392), -53vw -73vh 1px 0.75px rgba(255, 250, 247, 0.9158435917), -39vw 28vh 1px 0.75px #f8f8fa, 73vw -66vh 1px 0.75px #f1fbf5, 82vw -1vh 1px 0.75px rgba(242, 242, 242, 0.8414716373), 92vw -27vh 1px 0.75px rgba(245, 251, 250, 0.9663508769), 28vw -64vh 1px 0.75px #f1f9f1, 76vw 82vh 1px 0.75px #f1f9f6, 53vw -70vh 1px 0.75px #f7fdf7, -11vw -70vh 1px 0.75px rgba(251, 253, 241, 0.5709810522), -73vw -40vh 1px 0.75px #f9f1fb, -36vw 41vh 1px 0.75px #fdf1fe, -12vw -9vh 1px 0.75px rgba(247, 244, 242, 0.8454906488), 60vw 62vh 1px 0.75px #fdfaf4, -83vw 87vh 1px 0.75px rgba(245, 241, 244, 0.9176305322), 44vw -54vh 1px 0.75px rgba(242, 252, 248, 0.9693348061), 36vw -32vh 1px 0.75px #f9fafa, 41vw -61vh 1px 0.75px #f2faf8, 42vw -49vh 1px 0.75px rgba(249, 246, 254, 0.9134938038), -52vw 53vh 1px 0.75px #f3feff, 3vw -97vh 1px 0.75px rgba(249, 245, 247, 0.8889316068), -90vw 46vh 1px 0.75px #f8fafd, 23vw -29vh 1px 0.75px rgba(246, 247, 250, 0.6862141599), -33vw -63vh 1px 0.75px rgba(243, 253, 249, 0.6395580736), -2vw 50vh 1px 0.75px #f5faff, -96vw 67vh 1px 0.75px rgba(255, 241, 243, 0.8407892564), 29vw -95vh 1px 0.75px #f5f5f3, 3vw -27vh 1px 0.75px #fbfef6, -87vw 83vh 1px 0.75px rgba(253, 248, 246, 0.8165139252), 47vw 57vh 1px 0.75px #f9f5f1, -9vw -14vh 1px 0.75px rgba(245, 243, 246, 0.6535044176), 54vw 31vh 1px 0.75px #fff5f3, 25vw -52vh 1px 0.75px #f7faf3, 91vw -6vh 1px 0.75px #f8fff8, -21vw 88vh 1px 0.75px rgba(253, 245, 248, 0.7648848183), -29vw 45vh 1px 0.75px rgba(253, 241, 244, 0.6657591369), 61vw -47vh 1px 0.75px #f3fcf6, 57vw 23vh 1px 0.75px #f9f3fa, -90vw 100vh 1px 0.75px rgba(251, 244, 248, 0.9775995177), -27vw -93vh 1px 0.75px rgba(248, 251, 250, 0.5803350999), 28vw -29vh 1px 0.75px #f9f4fc, -80vw 67vh 1px 0.75px #f9f9fe, 90vw -18vh 1px 0.75px rgba(251, 251, 252, 0.6814187659), -82vw 27vh 1px 0.75px rgba(244, 248, 243, 0.893709482), -66vw 33vh 1px 0.75px #f1f8fc, 97vw 2vh 1px 0.75px rgba(244, 250, 241, 0.5214614359), -31vw -34vh 1px 0.75px rgba(245, 248, 244, 0.5625745825), 40vw -33vh 1px 0.75px #f6f6f9, 10vw 47vh 1px 0.75px #fefcf4, -49vw -20vh 1px 0.75px #f3f3f7, -61vw -11vh 1px 0.75px rgba(250, 244, 248, 0.7422571864), -67vw -2vh 1px 0.75px rgba(254, 243, 243, 0.8733439445), -39vw 17vh 1px 0.75px #f8f2f1, -36vw -56vh 1px 0.75px #f8faf6, 18vw -36vh 1px 0.75px rgba(249, 255, 248, 0.7808456449), -91vw 37vh 1px 0.75px #fcfef1, -67vw -26vh 1px 0.75px #f4f4fc, 50vw -79vh 1px 0.75px rgba(245, 244, 252, 0.6027408961), 73vw 82vh 1px 0.75px rgba(243, 252, 255, 0.575434172), -24vw -97vh 1px 0.75px #f4f6ff, -87vw -23vh 1px 0.75px #fffcf9, 81vw 78vh 1px 0.75px rgba(250, 251, 255, 0.800520426), -18vw -98vh 1px 0.75px #fbfefc, 19vw -33vh 1px 0.75px #fefdff, -36vw -21vh 1px 0.75px #f3f4f2, 16vw -39vh 1px 0.75px rgba(243, 254, 247, 0.726988897), 47vw -57vh 1px 0.75px rgba(248, 244, 245, 0.5195989269), -83vw -81vh 1px 0.75px #fefdf2, -44vw -19vh 1px 0.75px rgba(245, 242, 243, 0.8433148122), 58vw -37vh 1px 0.75px rgba(255, 245, 249, 0.9858396814), 61vw -76vh 1px 0.75px rgba(249, 247, 245, 0.7973323753), -59vw 45vh 1px 0.75px rgba(246, 254, 254, 0.5407901041), -20vw 73vh 1px 0.75px #fef8f1, -57vw -77vh 1px 0.75px #fefff2, -84vw 20vh 1px 0.75px rgba(247, 250, 254, 0.620131291), 77vw 20vh 1px 0.75px #f6fafd, 87vw -15vh 1px 0.75px rgba(249, 250, 246, 0.7752325345), 19vw 60vh 1px 0.75px #fbfafb, 84vw 37vh 1px 0.75px #f5fef7, 69vw -79vh 1px 0.75px #fff4fb, 64vw -90vh 1px 0.75px #f2f2f2, 70vw 21vh 1px 0.75px #fcf8f3, -63vw 86vh 1px 0.75px rgba(241, 253, 250, 0.881736872), -99vw -89vh 1px 0.75px rgba(255, 255, 245, 0.808938129), 13vw 58vh 1px 0.75px rgba(251, 247, 251, 0.6117021217), 69vw -43vh 1px 0.75px rgba(241, 252, 255, 0.7327540053), 35vw -94vh 1px 0.75px #f4fbf5, 21vw -13vh 1px 0.75px rgba(251, 253, 244, 0.5327758874), -6vw -99vh 1px 0.75px #fdfdf4, -28vw -81vh 1px 0.75px #fdf6fb, -91vw 39vh 1px 0.75px #f1f2f1;
  width: 1px;
  height: 1px;
}
div.starfield .moving-1 {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 3px;
  height: 3px;
  border-radius: 100%;
  transform-origin: -31vw -38vh;
  box-shadow: 98vw -71vh 1px 0.75px rgba(251, 254, 249, 0.5796560731), 60vw -74vh 1px 0.75px #f8f7f7, 28vw 43vh 1px 0.75px rgba(244, 241, 248, 0.6620956293), 46vw -74vh 1px 0.75px #f5f9f9, 23vw 30vh 1px 0.75px #f2f6f2, -63vw 77vh 1px 0.75px #f6f6f9, -30vw 20vh 1px 0.75px rgba(255, 254, 242, 0.95287468), -55vw -26vh 1px 0.75px rgba(241, 244, 253, 0.883166328), 26vw 2vh 1px 0.75px #f2f3f7, 71vw -60vh 1px 0.75px rgba(248, 252, 241, 0.5509532549), 74vw -17vh 1px 0.75px rgba(249, 246, 244, 0.7413498004), 96vw 16vh 1px 0.75px rgba(247, 246, 253, 0.6869293477), -36vw 53vh 1px 0.75px #fbf8f5, 22vw 94vh 1px 0.75px #f1f2fe, -59vw 90vh 1px 0.75px #f5f5f1, 45vw 59vh 1px 0.75px #fbf2f5, -59vw 90vh 1px 0.75px rgba(252, 245, 252, 0.937959723), 42vw -83vh 1px 0.75px rgba(244, 251, 247, 0.759251987), -29vw 83vh 1px 0.75px #f5fdf4, 34vw -96vh 1px 0.75px rgba(248, 254, 254, 0.547830485), 16vw -5vh 1px 0.75px rgba(251, 245, 252, 0.8815244488), 69vw 64vh 1px 0.75px #fcfcf4, 13vw -25vh 1px 0.75px rgba(254, 251, 242, 0.9875626299), 94vw -52vh 1px 0.75px rgba(246, 249, 244, 0.7827653594), 53vw 40vh 1px 0.75px #f5fbf3, -36vw 27vh 1px 0.75px #f7faff, -16vw 48vh 1px 0.75px #fef8f9, -46vw -68vh 1px 0.75px rgba(253, 241, 252, 0.8559579086), -31vw -88vh 1px 0.75px rgba(241, 241, 243, 0.5032175611), 61vw 91vh 1px 0.75px rgba(251, 244, 248, 0.5219877666), -82vw 27vh 1px 0.75px #f4f9f7, 19vw -96vh 1px 0.75px #f5fdf4, -74vw -74vh 1px 0.75px #f2fef5, -30vw 86vh 1px 0.75px rgba(247, 250, 243, 0.996487551), 92vw -2vh 1px 0.75px rgba(252, 245, 246, 0.7235354241), 99vw 1vh 1px 0.75px rgba(248, 250, 250, 0.9360883972), 88vw -73vh 1px 0.75px #fafbfc, 77vw 96vh 1px 0.75px #faf6fa, 17vw 95vh 1px 0.75px rgba(244, 248, 241, 0.5054011713), 42vw 5vh 1px 0.75px rgba(246, 249, 254, 0.785195719), -97vw 68vh 1px 0.75px rgba(251, 245, 245, 0.7938347435), 7vw -95vh 1px 0.75px rgba(243, 249, 255, 0.5113630258), 75vw -99vh 1px 0.75px #f3fcf7, 23vw -96vh 1px 0.75px #f1fdfd, 7vw 37vh 1px 0.75px rgba(242, 251, 254, 0.7921848084), -76vw 50vh 1px 0.75px rgba(254, 251, 253, 0.5466062608), -89vw -47vh 1px 0.75px #f7f4fc, 64vw 60vh 1px 0.75px #f5f3f4, 35vw -93vh 1px 0.75px rgba(244, 244, 253, 0.9113821496), -32vw 14vh 1px 0.75px rgba(255, 249, 247, 0.5448893182), 93vw 79vh 1px 0.75px rgba(243, 253, 243, 0.5715573667), 30vw -37vh 1px 0.75px rgba(246, 252, 252, 0.5706466807), 56vw 4vh 1px 0.75px rgba(248, 248, 252, 0.8380061132), 29vw -89vh 1px 0.75px rgba(245, 251, 241, 0.7265369866), 85vw -99vh 1px 0.75px rgba(247, 248, 252, 0.7803772343), -10vw 9vh 1px 0.75px rgba(250, 249, 253, 0.5637405381), 44vw 22vh 1px 0.75px rgba(246, 243, 241, 0.8564573626), 79vw 67vh 1px 0.75px #fff4fe, 3vw -54vh 1px 0.75px rgba(253, 253, 254, 0.70205046), -36vw -14vh 1px 0.75px rgba(249, 250, 244, 0.9694684202), 32vw -34vh 1px 0.75px #fffffb, -6vw -95vh 1px 0.75px rgba(253, 252, 252, 0.9187564777), 96vw -45vh 1px 0.75px rgba(242, 250, 252, 0.8899203059), 79vw -68vh 1px 0.75px rgba(253, 245, 244, 0.7345401342), -19vw 60vh 1px 0.75px #f1fbfb, 33vw 97vh 1px 0.75px #fbf1ff, -28vw 25vh 1px 0.75px rgba(242, 248, 245, 0.5307262917), -41vw -98vh 1px 0.75px #faf6f1, -24vw -83vh 1px 0.75px #fff4f9, 45vw -20vh 1px 0.75px rgba(248, 243, 250, 0.5240054996), 28vw 39vh 1px 0.75px rgba(250, 243, 250, 0.5274017566), 90vw 31vh 1px 0.75px #f8f6f9, -96vw -21vh 1px 0.75px #f8f7f2, 96vw 9vh 1px 0.75px rgba(247, 251, 247, 0.7435375515), -64vw -52vh 1px 0.75px #f5f8f3, 95vw -87vh 1px 0.75px rgba(243, 248, 243, 0.9133779279), 39vw -71vh 1px 0.75px #fbfcfa, -58vw 0vh 1px 0.75px #fbf5f7, -99vw -40vh 1px 0.75px #fff8f1, 80vw 84vh 1px 0.75px rgba(243, 243, 254, 0.8503211098), -46vw 2vh 1px 0.75px rgba(253, 242, 245, 0.5192076591), -99vw 54vh 1px 0.75px rgba(252, 241, 246, 0.7197874037), -71vw -13vh 1px 0.75px rgba(245, 253, 242, 0.8075053814), 8vw 77vh 1px 0.75px #f5f1f1, 80vw -98vh 1px 0.75px #fff8f1, -35vw -4vh 1px 0.75px #f6f3fb, -5vw -90vh 1px 0.75px #fbfdf1, 90vw -29vh 1px 0.75px rgba(251, 249, 241, 0.807057173), -20vw 73vh 1px 0.75px rgba(246, 242, 252, 0.5286088298), -46vw 83vh 1px 0.75px #f2faf1, -18vw 13vh 1px 0.75px #f1f2fc, 70vw 50vh 1px 0.75px #fff9fa, -82vw -18vh 1px 0.75px rgba(249, 254, 255, 0.835895734), 27vw -52vh 1px 0.75px rgba(246, 243, 243, 0.5915919663), 79vw -97vh 1px 0.75px #f3faf6, -27vw -84vh 1px 0.75px rgba(246, 245, 249, 0.6195757299), 25vw 95vh 1px 0.75px #f6f6fd, -83vw 95vh 1px 0.75px rgba(248, 250, 255, 0.995678514), 18vw -96vh 1px 0.75px #fafbf9, -87vw -52vh 1px 0.75px #f6fcf4;
  animation: star-movement 9s cubic-bezier(0.55, 0, 1, 0.45) infinite, direction-movement 30s ease-in-out alternate infinite;
}
div.starfield .moving-2 {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 3px;
  height: 3px;
  border-radius: 100%;
  transform-origin: -31vw -38vh;
  box-shadow: 40vw -74vh 1px 0.75px rgba(243, 253, 243, 0.6976853084), -93vw 42vh 1px 0.75px rgba(241, 241, 245, 0.5218902844), -6vw 27vh 1px 0.75px #f3ffff, -21vw -9vh 1px 0.75px #fef9f9, 70vw -13vh 1px 0.75px rgba(245, 253, 246, 0.632556686), 24vw -76vh 1px 0.75px rgba(247, 241, 242, 0.6379328074), 36vw -80vh 1px 0.75px #f1faf9, 33vw -49vh 1px 0.75px #f4fdf6, 23vw 2vh 1px 0.75px rgba(244, 250, 246, 0.7003595363), -46vw 41vh 1px 0.75px #fef6f3, 49vw -62vh 1px 0.75px rgba(249, 251, 241, 0.7464029821), 36vw 62vh 1px 0.75px #f3fafe, 87vw 44vh 1px 0.75px #f8f6f6, 24vw 70vh 1px 0.75px rgba(243, 249, 250, 0.9627452695), -36vw 81vh 1px 0.75px rgba(245, 241, 250, 0.8348169547), 65vw -86vh 1px 0.75px rgba(251, 250, 255, 0.6697964858), -78vw 27vh 1px 0.75px rgba(251, 250, 245, 0.6678135489), -99vw 84vh 1px 0.75px #fbfbf2, -66vw 63vh 1px 0.75px rgba(248, 247, 251, 0.6429325607), 54vw -53vh 1px 0.75px rgba(245, 241, 252, 0.8622728604), -51vw -13vh 1px 0.75px #fcf7f2, -93vw -91vh 1px 0.75px rgba(246, 242, 242, 0.7987353667), 80vw 91vh 1px 0.75px #f8f5f3, 67vw -66vh 1px 0.75px #f9f3f1, 47vw 78vh 1px 0.75px rgba(245, 250, 244, 0.74760595), -22vw 57vh 1px 0.75px rgba(246, 247, 247, 0.7581258614), -96vw -5vh 1px 0.75px #f9f5f5, 82vw -26vh 1px 0.75px #f3f7fc, 62vw -6vh 1px 0.75px #f2f7f1, 49vw 83vh 1px 0.75px rgba(248, 251, 252, 0.7898566349), 30vw 45vh 1px 0.75px rgba(247, 241, 253, 0.8091298881), -24vw -54vh 1px 0.75px #f7fcf8, 53vw -22vh 1px 0.75px rgba(254, 244, 249, 0.573485027), -48vw 88vh 1px 0.75px #f3f8f8, -89vw -52vh 1px 0.75px rgba(247, 249, 249, 0.6259997331), 0vw 82vh 1px 0.75px #f1f7f5, -25vw 9vh 1px 0.75px rgba(252, 242, 255, 0.5956961941), -7vw 31vh 1px 0.75px rgba(253, 253, 247, 0.553963104), 35vw 62vh 1px 0.75px rgba(254, 245, 254, 0.8865271661), -9vw -53vh 1px 0.75px #f8fbfa, 10vw -81vh 1px 0.75px #faf4fa, -75vw 8vh 1px 0.75px #f6f1f2, 50vw -51vh 1px 0.75px #fbf9fc, 99vw 32vh 1px 0.75px #fffaf1, -98vw -1vh 1px 0.75px rgba(248, 245, 249, 0.5978577971), 79vw -65vh 1px 0.75px rgba(243, 243, 245, 0.8759631083), 18vw 29vh 1px 0.75px #f5f3f6, -90vw -94vh 1px 0.75px #f2f5fb, -4vw -51vh 1px 0.75px #f2fef6, 62vw 71vh 1px 0.75px #f9f6fc, 70vw 33vh 1px 0.75px rgba(243, 246, 249, 0.8400624172), -14vw 71vh 1px 0.75px rgba(249, 246, 241, 0.719868869), 77vw -49vh 1px 0.75px #f9f9f4, 56vw -68vh 1px 0.75px #f1f1f6, 87vw -50vh 1px 0.75px #fdf9fd, -13vw 24vh 1px 0.75px rgba(254, 255, 245, 0.8050234245), 20vw -5vh 1px 0.75px rgba(254, 249, 255, 0.7744509303), 29vw -22vh 1px 0.75px rgba(254, 252, 249, 0.8683826021), 54vw -35vh 1px 0.75px snow, 19vw -30vh 1px 0.75px #f5f5f3, 99vw -48vh 1px 0.75px rgba(245, 245, 249, 0.5407473116), -84vw -26vh 1px 0.75px #f7f5fe, 38vw -24vh 1px 0.75px #f1fbf6, -48vw 33vh 1px 0.75px rgba(246, 255, 254, 0.6254991258), 28vw 15vh 1px 0.75px rgba(250, 245, 253, 0.8310338887), 73vw 91vh 1px 0.75px rgba(253, 242, 254, 0.7767698708), -65vw 96vh 1px 0.75px #f4f1f8, 8vw -48vh 1px 0.75px rgba(253, 246, 246, 0.8645967089), -22vw -85vh 1px 0.75px #f9f3f8, 36vw 63vh 1px 0.75px rgba(254, 253, 241, 0.6785751691), 99vw 37vh 1px 0.75px rgba(247, 247, 247, 0.582057336), 56vw -76vh 1px 0.75px #fffcfd, 83vw -12vh 1px 0.75px rgba(247, 250, 248, 0.8136677806), 26vw -12vh 1px 0.75px #fef9fd, 100vw 82vh 1px 0.75px rgba(250, 242, 248, 0.6861693788), 73vw -21vh 1px 0.75px #fbfcfb, -91vw -24vh 1px 0.75px rgba(253, 245, 241, 0.7598353681), 94vw 49vh 1px 0.75px rgba(254, 252, 251, 0.7513094056), -18vw 14vh 1px 0.75px rgba(247, 241, 247, 0.8442176478), -80vw 99vh 1px 0.75px #fcf3f8, -21vw 22vh 1px 0.75px #fdfaf6, 9vw 56vh 1px 0.75px rgba(241, 252, 250, 0.9484880841), -86vw 63vh 1px 0.75px #fffff7, 97vw 93vh 1px 0.75px rgba(246, 243, 246, 0.7090860489), -47vw 100vh 1px 0.75px #fbf6f6, 57vw -62vh 1px 0.75px #f9f2f5, -60vw 60vh 1px 0.75px rgba(250, 248, 246, 0.8128038178), -63vw 85vh 1px 0.75px #fcfcf9, -76vw -58vh 1px 0.75px #f4faf4, -8vw 93vh 1px 0.75px rgba(253, 250, 245, 0.7038606127), -95vw 32vh 1px 0.75px rgba(242, 252, 245, 0.8777265504), 53vw -8vh 1px 0.75px #f7f9f6, 49vw -13vh 1px 0.75px #f5f9fd, -41vw -60vh 1px 0.75px #f1faf1, -67vw 41vh 1px 0.75px rgba(254, 255, 254, 0.7231559202), 2vw 68vh 1px 0.75px #f9f3f6, -63vw 100vh 1px 0.75px #fbf1fc, -82vw 41vh 1px 0.75px #f8fff9, -72vw 95vh 1px 0.75px rgba(246, 244, 254, 0.8975317144), -42vw -56vh 1px 0.75px rgba(248, 253, 247, 0.5535049695);
  animation: star-movement 9s -3s cubic-bezier(0.55, 0, 1, 0.45) infinite, direction-movement 30s ease-in-out alternate infinite;
}
div.starfield .moving-3 {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 3px;
  height: 3px;
  border-radius: 100%;
  transform-origin: -31vw -38vh;
  box-shadow: -44vw 6vh 1px 0.75px #f2f3fb, -80vw 15vh 1px 0.75px rgba(243, 250, 243, 0.516430015), 27vw -53vh 1px 0.75px #f3f7f6, -72vw 67vh 1px 0.75px #f2f7fd, 67vw -22vh 1px 0.75px #fef1fe, 84vw 49vh 1px 0.75px rgba(251, 243, 255, 0.6223981162), -99vw 33vh 1px 0.75px #fdf7f1, -19vw 69vh 1px 0.75px #f1f4f3, 24vw 62vh 1px 0.75px rgba(249, 244, 245, 0.847846961), -48vw 15vh 1px 0.75px rgba(252, 252, 243, 0.9462591898), -18vw 42vh 1px 0.75px rgba(242, 252, 248, 0.5148233329), -7vw -11vh 1px 0.75px rgba(254, 251, 242, 0.523792914), -37vw 21vh 1px 0.75px rgba(253, 251, 250, 0.6881076159), -47vw -23vh 1px 0.75px rgba(243, 242, 243, 0.6893285365), -79vw -28vh 1px 0.75px #f4f5fd, -33vw 85vh 1px 0.75px rgba(243, 241, 253, 0.8328932503), 63vw -7vh 1px 0.75px #fdf7ff, 94vw 26vh 1px 0.75px #f1fefc, -21vw 27vh 1px 0.75px rgba(244, 241, 251, 0.9303236812), -3vw -78vh 1px 0.75px rgba(254, 245, 245, 0.8262886947), 50vw -42vh 1px 0.75px #f6fef4, 75vw 7vh 1px 0.75px #fbf4f7, 52vw -37vh 1px 0.75px #f1f6f8, 11vw 64vh 1px 0.75px #f7f2fe, -89vw 23vh 1px 0.75px rgba(245, 242, 250, 0.5728733726), 38vw -48vh 1px 0.75px #f4fdf7, 61vw 66vh 1px 0.75px #f5f3f5, -25vw -43vh 1px 0.75px #f8f3f4, 6vw 77vh 1px 0.75px rgba(241, 246, 252, 0.7924307884), 66vw 56vh 1px 0.75px rgba(247, 247, 248, 0.7896422377), 95vw 2vh 1px 0.75px rgba(249, 247, 246, 0.590453276), 9vw -33vh 1px 0.75px #fefcff, -44vw -94vh 1px 0.75px rgba(250, 249, 248, 0.7011202204), 43vw 0vh 1px 0.75px #fdfaf8, 43vw 87vh 1px 0.75px rgba(250, 241, 246, 0.7653099926), -18vw -25vh 1px 0.75px rgba(241, 249, 241, 0.5380415257), -16vw 42vh 1px 0.75px #f9fef1, -82vw 62vh 1px 0.75px rgba(243, 241, 243, 0.7823410134), -88vw -31vh 1px 0.75px rgba(245, 249, 243, 0.5612056516), -84vw 69vh 1px 0.75px #f5fcf9, -40vw 4vh 1px 0.75px rgba(246, 245, 252, 0.6526124422), -18vw 32vh 1px 0.75px #f6f9f8, -26vw 47vh 1px 0.75px rgba(252, 247, 243, 0.5120760699), 11vw 74vh 1px 0.75px #f4f4f6, 88vw 64vh 1px 0.75px rgba(254, 242, 255, 0.6278793349), 1vw 100vh 1px 0.75px #fdf5fb, -12vw -52vh 1px 0.75px #fff1fa, -81vw 2vh 1px 0.75px #f9f9fa, -74vw 78vh 1px 0.75px rgba(253, 247, 250, 0.6531596142), 29vw 35vh 1px 0.75px rgba(254, 244, 244, 0.5155631285), -50vw 11vh 1px 0.75px #f4fcfc, -95vw -18vh 1px 0.75px rgba(245, 241, 241, 0.5907147267), 75vw 76vh 1px 0.75px rgba(252, 253, 245, 0.6198726616), 8vw 26vh 1px 0.75px rgba(242, 254, 244, 0.6958144356), -84vw -22vh 1px 0.75px #f6f5fd, 55vw -21vh 1px 0.75px #fdf1fa, -27vw 38vh 1px 0.75px rgba(241, 248, 248, 0.5923234092), -41vw -69vh 1px 0.75px rgba(244, 243, 250, 0.7658755818), 99vw 71vh 1px 0.75px rgba(253, 251, 248, 0.7687157225), -35vw -13vh 1px 0.75px rgba(254, 255, 246, 0.9431341704), -66vw -30vh 1px 0.75px #f8f3f2, -54vw -97vh 1px 0.75px #f5fffe, -31vw 73vh 1px 0.75px rgba(242, 249, 249, 0.6221945971), -78vw 51vh 1px 0.75px rgba(241, 255, 244, 0.9122044389), 74vw -79vh 1px 0.75px rgba(249, 250, 253, 0.6143912375), -85vw -54vh 1px 0.75px #fff7ff, 8vw -50vh 1px 0.75px #f3fbf5, -55vw 8vh 1px 0.75px #fff5f9, 49vw -98vh 1px 0.75px #f1f3fd, -87vw 27vh 1px 0.75px rgba(248, 242, 246, 0.7316524966), -50vw 43vh 1px 0.75px rgba(242, 255, 242, 0.9689863526), -81vw -81vh 1px 0.75px rgba(247, 245, 251, 0.7452617459), 8vw -90vh 1px 0.75px #f7f8f1, -10vw 52vh 1px 0.75px rgba(245, 251, 242, 0.8692656615), -30vw -52vh 1px 0.75px #f9fff1, 49vw -46vh 1px 0.75px #f2f6f8, 63vw 99vh 1px 0.75px #f7f9fd, -67vw 14vh 1px 0.75px #f1f8fc, 69vw 95vh 1px 0.75px #f3f3f2, 56vw -55vh 1px 0.75px rgba(247, 253, 253, 0.7457878729), -4vw 84vh 1px 0.75px rgba(242, 252, 243, 0.7350566017), -61vw -68vh 1px 0.75px rgba(245, 252, 243, 0.5956419672), -30vw 88vh 1px 0.75px rgba(247, 249, 245, 0.7603800395), 80vw 41vh 1px 0.75px #fefdf7, -33vw 88vh 1px 0.75px rgba(248, 252, 250, 0.6284337101), -45vw 3vh 1px 0.75px #fdf8fd, 71vw 57vh 1px 0.75px #fbfcf5, -67vw 36vh 1px 0.75px #fffef1, 16vw 32vh 1px 0.75px #f2fff9, -8vw 9vh 1px 0.75px rgba(249, 245, 243, 0.5095446679), -10vw -57vh 1px 0.75px rgba(253, 255, 246, 0.5957843896), 56vw 92vh 1px 0.75px rgba(244, 244, 248, 0.6443797484), 62vw 51vh 1px 0.75px rgba(254, 241, 255, 0.6773818306), -41vw 29vh 1px 0.75px rgba(245, 248, 242, 0.8849121272), 75vw 74vh 1px 0.75px #f3fefd, -20vw 91vh 1px 0.75px #fcfaf1, -49vw 56vh 1px 0.75px #fbfbf3, -80vw -50vh 1px 0.75px rgba(241, 241, 242, 0.8231185693), -97vw 13vh 1px 0.75px #f6faf2, 43vw -50vh 1px 0.75px rgba(253, 255, 252, 0.8920248562);
  animation: star-movement 9s -6s cubic-bezier(0.55, 0, 1, 0.45) infinite, direction-movement 30s ease-in-out alternate infinite;
}

@keyframes star-movement {
  0% {
    transform: scale(0.5) translateZ(0);
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  99% {
    opacity: 1;
  }
  100% {
    transform: scale(2) translateZ(0);
    opacity: 0;
  }
}
@keyframes direction-movement {
  from {
    transform-origin: -12vw -2vh;
  }
  to {
    transform-origin: 25vw 23vh;
  }
}
div.word {
  position: absolute;
  right: 1vw;
  text-align: right;
  font-size: 2vh;
  animation: word-appear 60s infinite;
  opacity: 0;
  transform: translate3d(calc(1vw + 100%), 0, 0);
}

@keyframes word-appear {
  0% {
    opacity: 0;
    transform: translate3d(calc(1vw + 100%), 0, 0);
  }
  2% {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
  80% {
    opacity: 0;
    transform: translate3d(0, 0, 0);
  }
}