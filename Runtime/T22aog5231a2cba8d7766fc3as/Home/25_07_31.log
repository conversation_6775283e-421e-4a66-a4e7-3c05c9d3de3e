[ 2025-07-31T00:22:36+08:00 ] ************** /Public/Dela/template/dela-template.css.map
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001525s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000349s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001248s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001741s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.

[ 2025-07-31T00:22:37+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001645s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000337s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001249s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001755s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:37+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001624s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001243s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001685s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:37+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001615s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000334s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001248s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001703s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:37+08:00 ] ************** /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001589s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000330s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001218s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001664s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:37+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001533s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000339s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001191s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001650s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:37+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.003813s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001222s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001674s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:37+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001563s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000508s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001293s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001925s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:39+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001792s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000350s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001444s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001913s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:39+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001672s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001223s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001666s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:40+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001600s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001208s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001645s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:40+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001597s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000330s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001281s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001728s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:41+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001784s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000355s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001285s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001761s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:41+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.002261s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000547s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002217s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002954s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:42+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001571s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000334s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001446s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001899s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:42+08:00 ] ************** /Login/upregister?account=rista31%40511.la&ecode=&lpwd=XGVFDNEWQ&invit=XGVFDNEWQ&type=2
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001537s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000356s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001299s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001791s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'XGVFDNEWQ' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'kEjFh' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','6cbb26630713780a4b4b7f1327a17aa5','5000.00000000','kEjFh','165','0','0','2','',',165','**************','非洲','1753892562','1','1')

[ 2025-07-31T00:22:43+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001499s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000315s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001203s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001636s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:43+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001522s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000333s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001283s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001734s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:44+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001533s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000438s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001737s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002327s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:45+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001666s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001240s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001678s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:45+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001531s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001255s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001701s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:45+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001656s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001215s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001655s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:45+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.003178s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000329s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001303s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001756s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:45+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001608s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000349s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001311s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001801s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:46+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001801s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001302s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001753s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:47+08:00 ] ************** /Ajaxtrade/getallcoin
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001517s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000390s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001359s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001934s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0003s ]
SQL: SELECT `coinname`,`id` FROM `tw_ctmarket` WHERE `status` = 1 AND `coinname` <> 'usdz'  [ RunTime:0.0002s ]
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.

[ 2025-07-31T00:22:47+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001777s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001232s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001674s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:47+08:00 ] ************** /Login/upregister?account=rista31%40511.la&ecode=&lpwd=XGVFDNEWQ&invit=XGVFDNEWQ&type=2
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001735s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000361s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001253s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001739s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'XGVFDNEWQ' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'vckBq' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','6cbb26630713780a4b4b7f1327a17aa5','5000.00000000','vckBq','165','0','0','2','',',165','**************','非洲','1753892567','1','1')

[ 2025-07-31T00:22:47+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001780s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001250s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001708s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:48+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001626s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000342s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001233s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001699s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:49+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000005s ]
INFO: [ app_init ] --END-- [ RunTime:0.001539s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000316s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001198s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001631s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:49+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001558s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001253s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001693s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:49+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.001857s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000335s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001251s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001716s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:50+08:00 ] ************** /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001541s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000339s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001179s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001634s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:51+08:00 ] ************** /Login/upregister?account=rista31%40511.la&ecode=&lpwd=XGVFDNEWQ&invit=XGVFDNEWQ&type=2
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001618s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001314s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001764s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'XGVFDNEWQ' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'RJPiH' LIMIT 1   [ RunTime:0.0003s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','6cbb26630713780a4b4b7f1327a17aa5','5000.00000000','RJPiH','165','0','0','2','',',165','**************','非洲','1753892571','1','1')

[ 2025-07-31T00:22:51+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001532s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001264s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001705s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:51+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001533s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001173s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001617s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:51+08:00 ] ************** /Ajaxtrade/obtain_usdz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001549s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000366s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001215s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001699s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_market` [ RunTime:0.0004s ]
SQL: SELECT `new_price`,`min_price`,`max_price`,`faxingjia`,`volume` FROM `tw_market` WHERE `name` = 'usdz_usdt' LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:52+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001548s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000313s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001231s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001669s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:52+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001591s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000367s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001210s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001722s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:52+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001666s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000354s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001320s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001796s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:52+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001552s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001210s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001651s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:53+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001562s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001254s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001723s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:54+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001583s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000309s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001185s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001609s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:54+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001594s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000309s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001185s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001611s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:55+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002447s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001207s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001645s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:56+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001620s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000330s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001275s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001724s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:56+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001650s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000409s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001237s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001766s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:57+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001588s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000327s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001206s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001648s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:57+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001526s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000315s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001279s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001719s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:57+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001586s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001231s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001670s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:57+08:00 ] ************** /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001555s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001193s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001643s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:58+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001996s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000335s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001251s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001711s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:58+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002511s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000335s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001389s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001845s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:01+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001674s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000314s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001177s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001606s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:01+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001541s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001181s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001625s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:02+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001509s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000316s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001177s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001609s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:03+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001595s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001238s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001680s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:04+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.001610s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001281s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001736s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:04+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.002626s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000498s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001588s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002248s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:05+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001594s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000330s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001235s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001684s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:05+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001571s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001266s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001707s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:06+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001551s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000306s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001269s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001683s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:07+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001628s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000351s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001198s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001666s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:07+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001827s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000330s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001250s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001700s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:07+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001767s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000333s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001442s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001901s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:07+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.001536s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001248s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001692s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:07+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001639s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000393s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001431s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001947s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:07+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.004160s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000395s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001263s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001789s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:09+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001567s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001218s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001658s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:09+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.002523s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000333s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001292s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001748s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:09+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001662s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001231s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001667s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:10+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001569s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000342s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001195s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001653s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:11+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001663s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000344s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001334s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001801s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:11+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001626s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000327s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001413s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001891s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:12+08:00 ] ************** /Login/upregister?account=test123&ecode=&lpwd=password123&invit=TESTCODE&type=2
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001611s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001321s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001771s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.

[ 2025-07-31T00:23:12+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001567s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000373s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001234s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001729s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:12+08:00 ] ************** /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001517s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000360s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001235s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001741s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:12+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001520s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000314s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001301s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001741s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:12+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001587s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001336s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001787s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:14+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001603s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000327s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001241s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001688s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:15+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001515s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000329s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001199s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001664s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:15+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001542s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001217s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001663s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:16+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001619s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001262s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001723s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:16+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001546s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000329s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001231s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001677s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:16+08:00 ] ************** /Ajaxtrade/obtain_usdz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001654s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000335s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001235s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001697s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_market` [ RunTime:0.0004s ]
SQL: SELECT `new_price`,`min_price`,`max_price`,`faxingjia`,`volume` FROM `tw_market` WHERE `name` = 'usdz_usdt' LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:18+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001598s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000339s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001309s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001773s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:18+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001583s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001397s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001841s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:19+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001609s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001274s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001710s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:19+08:00 ] ************** /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001573s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000340s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001299s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001766s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:19+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001518s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000313s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001239s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001676s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:20+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001639s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000333s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001295s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001752s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:20+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001688s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000347s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001331s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001825s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:21+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001555s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001398s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001845s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:21+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001539s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000434s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001264s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001820s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:21+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001547s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000350s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001222s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001706s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:22+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001524s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001230s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001686s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:22+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001530s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001200s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001641s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:24+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000004s ]
INFO: [ app_init ] --END-- [ RunTime:0.001525s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000315s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001214s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001646s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:24+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001556s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000351s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001256s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001727s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-31T00:23:24+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001593s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000347s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001219s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001712s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:25+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001600s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000333s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001294s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001759s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:26+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002471s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000382s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001316s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001817s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:26+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001803s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000379s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001332s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001849s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:27+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001680s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000354s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001346s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001822s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:28+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001561s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000327s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001229s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001673s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:29+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001727s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001233s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001680s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:30+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001683s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001218s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001657s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-31T00:23:30+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001581s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001230s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001668s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:31+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001647s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001243s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001694s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:31+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001591s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000334s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001266s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001814s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:32+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001617s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001215s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001660s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:32+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001526s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000356s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001203s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001675s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:32+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001528s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000327s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001225s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001671s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:33+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001525s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000312s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001238s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001670s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:33+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001500s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000310s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001198s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001624s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:34+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001599s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000329s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001257s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001706s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:35+08:00 ] ************** /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001594s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000334s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001272s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001725s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:36+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001531s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001222s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001661s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:36+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001613s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000340s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001287s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001750s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:36+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.004774s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000329s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001283s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001750s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:36+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001566s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001183s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001627s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:38+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001599s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000316s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001242s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001677s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:39+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.002576s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000349s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001534s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002003s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:39+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001748s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001314s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001764s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:39+08:00 ] ************** /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001673s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000473s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001266s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001864s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:40+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001617s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000498s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001464s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002114s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:41+08:00 ] ************** /Ajaxtrade/obtain_usdz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001564s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000392s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001205s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001729s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_market` [ RunTime:0.0004s ]
SQL: SELECT `new_price`,`min_price`,`max_price`,`faxingjia`,`volume` FROM `tw_market` WHERE `name` = 'usdz_usdt' LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:41+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001550s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001230s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001686s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:41+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001577s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000357s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001230s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001706s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:42+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001581s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000395s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001248s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001766s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:42+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001582s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001202s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001641s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:42+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001652s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001216s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001649s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:42+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000006s ]
INFO: [ app_init ] --END-- [ RunTime:0.001549s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000343s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001242s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001703s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:44+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001583s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000359s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001311s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001799s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:45+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001572s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000312s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001239s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001667s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:45+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001585s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000358s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.018180s ]
INFO: [ app_begin ] --END-- [ RunTime:0.018692s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:46+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001646s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001254s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001699s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:46+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001515s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000316s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001211s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001646s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:46+08:00 ] ************** /Ajaxtrade/getallcoin
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001560s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000335s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001255s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001710s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0003s ]
SQL: SELECT `coinname`,`id` FROM `tw_ctmarket` WHERE `status` = 1 AND `coinname` <> 'usdz'  [ RunTime:0.0002s ]
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.

[ 2025-07-31T00:23:46+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001603s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000345s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001237s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001701s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:46+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.005943s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002227s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002672s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:46+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001516s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000310s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001177s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001603s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:47+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001569s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000316s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001485s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001951s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:47+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001579s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001224s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001664s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:48+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001536s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001206s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001641s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:48+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001578s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001216s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001657s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:50+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001607s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001248s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001693s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:51+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002105s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000403s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001239s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001804s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:51+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001767s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000331s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001227s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001681s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:51+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.002535s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000354s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001206s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001684s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:51+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001558s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000311s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001276s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001705s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:52+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001583s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000347s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001240s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001713s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:53+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001605s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000331s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001441s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001905s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:53+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001761s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000349s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001236s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001708s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:54+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001676s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000327s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001254s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001710s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:54+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001564s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001199s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001638s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:56+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001643s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000339s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001278s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001738s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:56+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001621s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001338s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001790s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:57+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001862s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000339s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001266s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001733s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:58+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001589s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001211s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001654s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:58+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001518s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000316s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001233s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001668s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:23:58+08:00 ] ************** /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001778s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001369s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001810s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:00+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001616s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000344s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001450s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001926s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-31T00:24:00+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001606s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000344s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001604s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002077s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-31T00:24:00+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001701s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000344s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001292s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001765s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:00+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001610s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000343s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001620s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002086s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:01+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001544s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001321s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001759s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:01+08:00 ] ************** /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000015s ]
INFO: [ app_init ] --END-- [ RunTime:0.004880s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002214s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002704s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:01+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001590s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000331s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001298s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001748s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:02+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002142s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000368s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001236s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001724s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:03+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001545s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000330s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001283s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001734s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:03+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001553s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000396s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001307s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001825s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:04+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001696s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001196s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001632s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:04+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001530s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000313s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001240s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001671s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:06+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001565s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001200s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001641s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:06+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000016s ]
INFO: [ app_init ] --END-- [ RunTime:0.011084s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000346s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001616s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002082s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:06+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001553s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001242s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001685s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:06+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.001585s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001243s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001687s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:06+08:00 ] ************** /Ajaxtrade/obtain_usdz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001636s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000331s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001212s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001663s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_market` [ RunTime:0.0004s ]
SQL: SELECT `new_price`,`min_price`,`max_price`,`faxingjia`,`volume` FROM `tw_market` WHERE `name` = 'usdz_usdt' LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:07+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001585s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000313s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001181s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001613s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:08+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001686s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001210s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001647s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:09+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001737s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000341s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001244s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001711s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:09+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001678s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001201s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001645s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:10+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001525s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001222s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001667s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:11+08:00 ] ************** /Home/Autoexe/hycarryout
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001567s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000350s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001276s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001739s ]
SQL: SHOW COLUMNS FROM `tw_hyorder` [ RunTime:0.0005s ]
SQL: SELECT * FROM `tw_hyorder` WHERE `status` = 1 AND `intselltime` <= 1753892651  [ RunTime:0.0004s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0003s ]
SQL: SELECT `hy_ksid`,`hy_ylid`,`hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000362s ]
INFO: [ app_end ] --END-- [ RunTime:0.000427s ]

[ 2025-07-31T00:24:11+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001530s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000367s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001243s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001742s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:11+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001544s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000345s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001219s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001699s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:12+08:00 ] ************** /Home/Autoexe/hycarryout_ty
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001503s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000314s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001215s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001639s ]
SQL: SHOW COLUMNS FROM `tw_tyhyorder` [ RunTime:0.0005s ]
SQL: SELECT * FROM `tw_tyhyorder` WHERE `status` = 1 AND `intselltime` <= 1753892652  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0003s ]
SQL: SELECT `hy_ksid`,`hy_ylid`,`hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000399s ]
INFO: [ app_end ] --END-- [ RunTime:0.000478s ]

[ 2025-07-31T00:24:12+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001647s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001188s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001623s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:12+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001693s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001191s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001649s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:13+08:00 ] ************** /Home/Autoexe/setwl
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001537s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000350s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001208s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001674s ]
SQL: SHOW COLUMNS FROM `tw_hyorder` [ RunTime:0.0004s ]
SQL: SELECT COUNT(*) AS tp_count FROM `tw_hyorder` WHERE `status` = 1 AND `kongyk` = 0 LIMIT 1   [ RunTime:0.0004s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0003s ]
SQL: SELECT `hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000404s ]
INFO: [ app_end ] --END-- [ RunTime:0.000504s ]

[ 2025-07-31T00:24:14+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001682s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001198s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001652s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:14+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001530s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000316s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001214s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001647s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:15+08:00 ] ************** /Home/Autoexe/setwl_ty
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001511s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000333s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001322s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001767s ]
SQL: SHOW COLUMNS FROM `tw_tyhyorder` [ RunTime:0.0004s ]
SQL: SELECT COUNT(*) AS tp_count FROM `tw_tyhyorder` WHERE `status` = 1 AND `kongyk` = 0 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0003s ]
SQL: SELECT `hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000341s ]
INFO: [ app_end ] --END-- [ RunTime:0.000402s ]

[ 2025-07-31T00:24:15+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001679s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000316s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001212s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001644s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:15+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001585s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001228s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001673s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:16+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001608s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001203s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001628s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:16+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001544s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001414s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001857s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:16+08:00 ] ************** /Home/Autoexe/autoxjtade
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001533s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001296s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001723s ]
SQL: SHOW COLUMNS FROM `tw_bborder` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_bborder` WHERE `ordertype` = 1 AND `status` = 1  [ RunTime:0.0002s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000390s ]
INFO: [ app_end ] --END-- [ RunTime:0.000453s ]

[ 2025-07-31T00:24:18+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001568s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001289s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001729s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:18+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001540s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001226s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001701s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:18+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001609s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000395s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001358s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001874s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:19+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001522s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000330s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001180s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001644s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:20+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001582s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001238s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001682s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:20+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001616s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000330s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001423s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001871s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:21+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001535s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001195s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001639s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:21+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001567s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000334s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001263s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001716s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:21+08:00 ] ************** /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001540s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001254s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001697s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:21+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001575s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002495s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002946s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:22+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001543s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.007517s ]
INFO: [ app_begin ] --END-- [ RunTime:0.007986s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:22+08:00 ] ************** /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001558s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000359s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001264s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001741s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:22+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001566s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001193s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001633s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:24+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001533s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001217s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001669s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:24+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.001560s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000329s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001279s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001727s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:24+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001545s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000372s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001236s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001726s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:26+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001616s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001236s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001685s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:26+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001520s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001223s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001663s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:26+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001547s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001327s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001765s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:27+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001574s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001221s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001658s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:27+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001620s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000366s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001257s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001771s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:28+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001544s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001218s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001658s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:28+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001534s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001199s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001640s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:30+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001647s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001215s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001664s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:30+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001652s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000340s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001321s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001783s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:31+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001843s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000390s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001359s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001873s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:31+08:00 ] ************** /Ajaxtrade/obtain_usdz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001559s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001476s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001927s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_market` [ RunTime:0.0004s ]
SQL: SELECT `new_price`,`min_price`,`max_price`,`faxingjia`,`volume` FROM `tw_market` WHERE `name` = 'usdz_usdt' LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-31T00:24:32+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001529s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000315s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001257s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001691s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:33+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001614s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000333s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001199s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001648s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:34+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001734s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000344s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001248s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001714s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:35+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001549s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000327s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001242s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001688s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-31T00:24:36+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001560s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000409s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001198s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001724s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:36+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001632s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000351s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001244s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001714s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:36+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001498s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000316s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001209s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001640s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:36+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001622s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001248s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001685s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:37+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001635s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000315s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001238s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001671s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:37+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001601s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000374s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001278s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001778s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:37+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001561s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000344s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001225s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001686s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:37+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001636s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001254s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001700s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:38+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001594s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001203s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001642s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:38+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001546s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001383s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001825s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:39+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001683s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000327s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001238s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001693s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:40+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.002300s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000429s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001799s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002385s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:41+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001549s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000352s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001656s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002136s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:42+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001613s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001308s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001745s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:42+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001518s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001287s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001738s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:42+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002520s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000343s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001207s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001675s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:42+08:00 ] ************** /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001570s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001216s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001657s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:44+08:00 ] ************** /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001568s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000343s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001242s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001704s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:44+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001774s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000346s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001234s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001707s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:45+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001676s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000368s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001371s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001862s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:45+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001650s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001269s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001715s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:45+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001600s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000346s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.004318s ]
INFO: [ app_begin ] --END-- [ RunTime:0.004785s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:45+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001635s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001248s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001689s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:46+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001624s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000315s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001244s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001675s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:46+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001704s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000413s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001516s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002074s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:47+08:00 ] ************** /Ajaxtrade/getallcoin
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001632s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000330s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001322s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001767s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0003s ]
SQL: SELECT `coinname`,`id` FROM `tw_ctmarket` WHERE `status` = 1 AND `coinname` <> 'usdz'  [ RunTime:0.0002s ]
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.

[ 2025-07-31T00:24:47+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001614s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001257s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001748s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:47+08:00 ] ************** /Login/register/Lang/ja-jp
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000025s ]
INFO: [ app_init ] --END-- [ RunTime:0.001802s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001260s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001717s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000072s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000163s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8] Undefined variable: uid /www/wwwroot/www.3728.ltd/Runtime/Cache/Home/524a08806bc29449645d93a7a8f8e680.php 第 237 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8] Undefined variable: qrcode /www/wwwroot/www.3728.ltd/Runtime/Cache/Home/524a08806bc29449645d93a7a8f8e680.php 第 557 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.011413s ]
INFO: [ view_parse ] --END-- [ RunTime:0.011486s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000192s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000248s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000358s ]
INFO: [ app_end ] --END-- [ RunTime:0.000437s ]

[ 2025-07-31T00:24:47+08:00 ] ************** /Public/Dela/template/dela-template.css.map
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001551s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001186s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001641s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.

[ 2025-07-31T00:24:48+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001732s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001280s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001739s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:48+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001618s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001235s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001678s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:48+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001568s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001270s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001705s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:49+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001558s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001234s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001678s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:50+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001565s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000329s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002305s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002773s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:50+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002610s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000486s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001833s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002478s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:51+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.001595s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001234s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001680s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:51+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001597s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000348s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001254s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001732s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:51+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001512s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001252s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001686s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:52+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001508s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000338s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001170s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001627s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:54+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001541s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000463s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001872s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002496s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:54+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.002003s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001251s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001705s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:54+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001860s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000393s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001369s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001891s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:54+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001516s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000313s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001262s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001695s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:56+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001548s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001237s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001675s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:57+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001770s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001481s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001934s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:57+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001545s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000335s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001271s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001739s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:57+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001934s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001250s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001698s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:57+08:00 ] ************** /Ajaxtrade/obtain_usdz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002979s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001389s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001838s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_market` [ RunTime:0.0005s ]
SQL: SELECT `new_price`,`min_price`,`max_price`,`faxingjia`,`volume` FROM `tw_market` WHERE `name` = 'usdz_usdt' LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:58+08:00 ] ************** /Ajaxtrade/getallcoin
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001589s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000337s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001270s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001730s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0003s ]
SQL: SELECT `coinname`,`id` FROM `tw_ctmarket` WHERE `status` = 1 AND `coinname` <> 'usdz'  [ RunTime:0.0002s ]
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.

[ 2025-07-31T00:24:58+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001518s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000316s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001215s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001649s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:58+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001613s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001214s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001651s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:58+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001501s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001211s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001646s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:58+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001555s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000376s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001209s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001713s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:58+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001553s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001224s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001664s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:58+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001596s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001229s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001673s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:58+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.002437s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000478s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001826s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002485s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:24:59+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001552s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001230s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001690s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:00+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001550s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001253s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001713s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:00+08:00 ] ************** /Ajaxtrade/getallcoin
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001997s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000400s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001571s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002117s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0003s ]
SQL: SELECT `coinname`,`id` FROM `tw_ctmarket` WHERE `status` = 1 AND `coinname` <> 'usdz'  [ RunTime:0.0002s ]
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.

[ 2025-07-31T00:25:01+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001517s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000314s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001188s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001637s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:01+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001636s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001224s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001663s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:01+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001568s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001245s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001685s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:01+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002232s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000339s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001342s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001819s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0005s ]

[ 2025-07-31T00:25:01+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001526s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000414s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001297s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001840s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:01+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.002740s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000355s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001299s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001841s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'woEAr' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','4f4eee2ca91ec3bc0611cef71641bf42','5000.00000000','woEAr','163','0','0','2','',',163','**************','非洲','1753892701','1','1')

[ 2025-07-31T00:25:01+08:00 ] ************** /Home/Autoexe/hycarryout_ty
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002075s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000353s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001595s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002074s ]
SQL: SHOW COLUMNS FROM `tw_tyhyorder` [ RunTime:0.0005s ]
SQL: SELECT * FROM `tw_tyhyorder` WHERE `status` = 1 AND `intselltime` <= 1753892701  [ RunTime:0.0004s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0006s ]
SQL: SELECT `hy_ksid`,`hy_ylid`,`hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000391s ]
INFO: [ app_end ] --END-- [ RunTime:0.000460s ]

[ 2025-07-31T00:25:01+08:00 ] ************** /Home/Autoexe/hycarryout
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001878s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000364s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001296s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001793s ]
SQL: SHOW COLUMNS FROM `tw_hyorder` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_hyorder` WHERE `status` = 1 AND `intselltime` <= 1753892701  [ RunTime:0.0004s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0003s ]
SQL: SELECT `hy_ksid`,`hy_ylid`,`hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000429s ]
INFO: [ app_end ] --END-- [ RunTime:0.000496s ]

[ 2025-07-31T00:25:01+08:00 ] ************** /Home/Autoexe/setwl
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.001960s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000333s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001293s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001738s ]
SQL: SHOW COLUMNS FROM `tw_hyorder` [ RunTime:0.0005s ]
SQL: SELECT COUNT(*) AS tp_count FROM `tw_hyorder` WHERE `status` = 1 AND `kongyk` = 0 LIMIT 1   [ RunTime:0.0004s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0003s ]
SQL: SELECT `hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000561s ]
INFO: [ app_end ] --END-- [ RunTime:0.000662s ]

[ 2025-07-31T00:25:01+08:00 ] ************** /Home/Autoexe/hycarryout
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.002630s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000494s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002005s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002653s ]
SQL: SHOW COLUMNS FROM `tw_hyorder` [ RunTime:0.0003s ]
SQL: SELECT * FROM `tw_hyorder` WHERE `status` = 1 AND `intselltime` <= 1753892701  [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0003s ]
SQL: SELECT `hy_ksid`,`hy_ylid`,`hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000561s ]
INFO: [ app_end ] --END-- [ RunTime:0.000635s ]

[ 2025-07-31T00:25:01+08:00 ] ************** /Home/Autoexe/setwl_ty
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001528s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000327s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001206s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001646s ]
SQL: SHOW COLUMNS FROM `tw_tyhyorder` [ RunTime:0.0005s ]
SQL: SELECT COUNT(*) AS tp_count FROM `tw_tyhyorder` WHERE `status` = 1 AND `kongyk` = 0 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0003s ]
SQL: SELECT `hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000373s ]
INFO: [ app_end ] --END-- [ RunTime:0.000440s ]

[ 2025-07-31T00:25:02+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001520s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000315s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001220s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001653s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:03+08:00 ] ************** /Home/Autoexe/hycarryout_ty
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.002125s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001211s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001645s ]
SQL: SHOW COLUMNS FROM `tw_tyhyorder` [ RunTime:0.0005s ]
SQL: SELECT * FROM `tw_tyhyorder` WHERE `status` = 1 AND `intselltime` <= 1753892703  [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0003s ]
SQL: SELECT `hy_ksid`,`hy_ylid`,`hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000365s ]
INFO: [ app_end ] --END-- [ RunTime:0.000429s ]

[ 2025-07-31T00:25:03+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001565s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001219s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001659s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:03+08:00 ] ************** /Ajaxtrade/getallcoin
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001567s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001210s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001650s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0004s ]
SQL: SELECT `coinname`,`id` FROM `tw_ctmarket` WHERE `status` = 1 AND `coinname` <> 'usdz'  [ RunTime:0.0003s ]
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.

[ 2025-07-31T00:25:04+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001535s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000327s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001444s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001894s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:04+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002048s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001247s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001686s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:04+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001728s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000389s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001253s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001764s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:04+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001649s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001329s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001771s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:04+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001590s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001236s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001679s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:04+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.002420s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001571s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002053s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:04+08:00 ] ************** /Home/Autoexe/setwl
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001489s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000357s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001303s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001771s ]
SQL: SHOW COLUMNS FROM `tw_hyorder` [ RunTime:0.0007s ]
SQL: SELECT COUNT(*) AS tp_count FROM `tw_hyorder` WHERE `status` = 1 AND `kongyk` = 0 LIMIT 1   [ RunTime:0.0004s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0003s ]
SQL: SELECT `hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000360s ]
INFO: [ app_end ] --END-- [ RunTime:0.000424s ]

[ 2025-07-31T00:25:04+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001581s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001483s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001962s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:04+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001568s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000316s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001184s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001617s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:04+08:00 ] ************** /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001566s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001237s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001687s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:04+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001579s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000343s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001257s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001721s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:04+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001674s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001977s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002419s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:04+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.001703s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000342s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001289s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001785s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:05+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001591s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000327s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001233s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001679s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:05+08:00 ] ************** /Home/Autoexe/setwl_ty
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002577s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000343s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001279s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001739s ]
SQL: SHOW COLUMNS FROM `tw_tyhyorder` [ RunTime:0.0005s ]
SQL: SELECT COUNT(*) AS tp_count FROM `tw_tyhyorder` WHERE `status` = 1 AND `kongyk` = 0 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0003s ]
SQL: SELECT `hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000366s ]
INFO: [ app_end ] --END-- [ RunTime:0.000429s ]

[ 2025-07-31T00:25:06+08:00 ] ************** /index.php?s=/Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001582s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001327s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001771s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.

[ 2025-07-31T00:25:06+08:00 ] ************** /Ajaxtrade/getallcoin
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001517s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001272s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001710s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0003s ]
SQL: SELECT `coinname`,`id` FROM `tw_ctmarket` WHERE `status` = 1 AND `coinname` <> 'usdz'  [ RunTime:0.0002s ]
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.

[ 2025-07-31T00:25:07+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001616s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001310s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001758s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:07+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001589s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000351s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001332s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001823s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:07+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002299s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000329s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001266s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001713s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:07+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001708s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000336s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001339s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001800s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:07+08:00 ] ************** /Home/Autoexe/autoxjtade
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002288s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000445s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001680s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002292s ]
SQL: SHOW COLUMNS FROM `tw_bborder` [ RunTime:0.0006s ]
SQL: SELECT * FROM `tw_bborder` WHERE `ordertype` = 1 AND `status` = 1  [ RunTime:0.0003s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000496s ]
INFO: [ app_end ] --END-- [ RunTime:0.000593s ]

[ 2025-07-31T00:25:07+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.001584s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001285s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001725s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:07+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001636s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001430s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001877s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:07+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001529s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000353s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001257s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001736s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:08+08:00 ] ************** /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001753s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000331s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001234s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001685s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:09+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001608s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001233s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001668s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:09+08:00 ] ************** /Ajaxtrade/getallcoin
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001557s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000409s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001219s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001749s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0003s ]
SQL: SELECT `coinname`,`id` FROM `tw_ctmarket` WHERE `status` = 1 AND `coinname` <> 'usdz'  [ RunTime:0.0002s ]
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.

[ 2025-07-31T00:25:09+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001550s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001290s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001734s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:09+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001539s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001258s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001702s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:10+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001595s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001293s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001739s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:10+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001596s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001277s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001728s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:10+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001543s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000315s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001196s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001629s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:10+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001688s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000372s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002086s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002607s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:10+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.002386s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000335s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001322s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001783s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:11+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000032s ]
INFO: [ app_init ] --END-- [ RunTime:0.001533s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001207s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001643s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:11+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001585s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001203s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001641s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:11+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.001729s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001205s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001647s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:12+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001513s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000312s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001204s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001635s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:12+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001600s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000342s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001279s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001742s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:12+08:00 ] ************** /Ajaxtrade/getallcoin
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001515s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001210s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001654s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0003s ]
SQL: SELECT `coinname`,`id` FROM `tw_ctmarket` WHERE `status` = 1 AND `coinname` <> 'usdz'  [ RunTime:0.0002s ]
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.

[ 2025-07-31T00:25:12+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001543s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001227s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001671s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:12+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001633s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000354s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001381s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001855s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:13+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001524s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001281s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001730s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:13+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001558s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000316s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001206s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001645s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:13+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001528s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001189s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001631s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:13+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001504s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000307s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001199s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001655s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:15+08:00 ] ************** /Home/Autoexe/setwl
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.001596s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000327s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001233s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001677s ]
SQL: SHOW COLUMNS FROM `tw_hyorder` [ RunTime:0.0005s ]
SQL: SELECT COUNT(*) AS tp_count FROM `tw_hyorder` WHERE `status` = 1 AND `kongyk` = 0 LIMIT 1   [ RunTime:0.0004s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0003s ]
SQL: SELECT `hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000358s ]
INFO: [ app_end ] --END-- [ RunTime:0.000420s ]

[ 2025-07-31T00:25:15+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001506s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000316s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001337s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001773s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:15+08:00 ] ************** /Ajaxtrade/getallcoin
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.001584s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000342s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001237s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001697s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0003s ]
SQL: SELECT `coinname`,`id` FROM `tw_ctmarket` WHERE `status` = 1 AND `coinname` <> 'usdz'  [ RunTime:0.0002s ]
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.

[ 2025-07-31T00:25:15+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001560s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000333s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001221s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001671s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:15+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001528s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000315s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001169s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001599s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:16+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001558s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001411s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001865s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:16+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001633s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000353s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001280s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001757s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:16+08:00 ] ************** /Home/Autoexe/setwl_ty
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001548s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001197s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001628s ]
SQL: SHOW COLUMNS FROM `tw_tyhyorder` [ RunTime:0.0004s ]
SQL: SELECT COUNT(*) AS tp_count FROM `tw_tyhyorder` WHERE `status` = 1 AND `kongyk` = 0 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0003s ]
SQL: SELECT `hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0020s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000372s ]
INFO: [ app_end ] --END-- [ RunTime:0.000439s ]

[ 2025-07-31T00:25:16+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001633s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001709s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002176s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:16+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001634s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000378s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001346s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001845s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:16+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001565s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002235s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002683s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-31T00:25:16+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001559s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001251s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001697s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:16+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001522s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000360s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001201s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001679s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:16+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002245s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000390s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001346s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001886s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:16+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001566s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000314s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001256s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001687s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:17+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001647s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000327s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001283s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001730s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:17+08:00 ] ************** /Home/Autoexe/autoxjtade
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001604s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000387s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001325s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001844s ]
SQL: SHOW COLUMNS FROM `tw_bborder` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_bborder` WHERE `ordertype` = 1 AND `status` = 1  [ RunTime:0.0003s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000357s ]
INFO: [ app_end ] --END-- [ RunTime:0.000422s ]

[ 2025-07-31T00:25:18+08:00 ] ************** /Ajaxtrade/getallcoin
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001552s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000341s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001245s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001706s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0014s ]
SQL: SELECT `coinname`,`id` FROM `tw_ctmarket` WHERE `status` = 1 AND `coinname` <> 'usdz'  [ RunTime:0.0002s ]
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.

[ 2025-07-31T00:25:18+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001587s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001274s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001732s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:18+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001523s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000345s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001252s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001717s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:18+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001524s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001184s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001620s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:18+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001595s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001206s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001648s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:19+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001518s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000310s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001188s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001617s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:19+08:00 ] ************** /Home/Autoexe/hycarryout_ty
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001562s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001245s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001683s ]
SQL: SHOW COLUMNS FROM `tw_tyhyorder` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_tyhyorder` WHERE `status` = 1 AND `intselltime` <= 1753892719  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0003s ]
SQL: SELECT `hy_ksid`,`hy_ylid`,`hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000353s ]
INFO: [ app_end ] --END-- [ RunTime:0.000416s ]

[ 2025-07-31T00:25:20+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001590s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001202s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001653s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:20+08:00 ] ************** /Home/Autoexe/hycarryout
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001497s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001290s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001725s ]
SQL: SHOW COLUMNS FROM `tw_hyorder` [ RunTime:0.0005s ]
SQL: SELECT * FROM `tw_hyorder` WHERE `status` = 1 AND `intselltime` <= 1753892720  [ RunTime:0.0004s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0003s ]
SQL: SELECT `hy_ksid`,`hy_ylid`,`hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000410s ]
INFO: [ app_end ] --END-- [ RunTime:0.000484s ]

[ 2025-07-31T00:25:20+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001708s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001337s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001822s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-31T00:25:21+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001521s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000407s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001224s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001752s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:21+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001778s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000421s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001571s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002128s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:21+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002033s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000411s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001614s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002196s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:22+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.008085s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000431s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001645s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002234s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:22+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001569s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000331s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001211s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001658s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:22+08:00 ] ************** /Ajaxtrade/obtain_usdz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001636s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000387s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001492s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002010s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_market` [ RunTime:0.0005s ]
SQL: SELECT `new_price`,`min_price`,`max_price`,`faxingjia`,`volume` FROM `tw_market` WHERE `name` = 'usdz_usdt' LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:22+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001691s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000333s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001545s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001999s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:22+08:00 ] ************** /Ajaxtrade/getallcoin
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.002577s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000548s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002077s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002817s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0003s ]
SQL: SELECT `coinname`,`id` FROM `tw_ctmarket` WHERE `status` = 1 AND `coinname` <> 'usdz'  [ RunTime:0.0002s ]
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.

[ 2025-07-31T00:25:22+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001566s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000340s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001243s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001700s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:22+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.002895s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000420s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001483s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002596s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:22+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001539s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000339s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001232s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001691s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:23+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001556s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001199s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001644s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:23+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001553s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001217s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001663s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:24+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001558s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001243s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001679s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:24+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001521s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001247s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001687s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:24+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001677s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001224s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001699s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:24+08:00 ] ************** /Ajaxtrade/getallcoin
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001616s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001208s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001655s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0003s ]
SQL: SELECT `coinname`,`id` FROM `tw_ctmarket` WHERE `status` = 1 AND `coinname` <> 'usdz'  [ RunTime:0.0002s ]
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.

[ 2025-07-31T00:25:24+08:00 ] ************** /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001523s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000476s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001258s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001881s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:25+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001539s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001202s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001642s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:25+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001531s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001197s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001622s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:25+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001597s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001232s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001681s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:26+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001537s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001249s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001688s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:26+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001599s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001611s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002068s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:26+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001555s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000330s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001218s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001671s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:26+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001555s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000329s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001264s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001712s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:26+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.002435s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000457s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001917s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002515s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:27+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000015s ]
INFO: [ app_init ] --END-- [ RunTime:0.001561s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001198s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001634s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:28+08:00 ] ************** /Ajaxtrade/getallcoin
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002520s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000480s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002038s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002681s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0003s ]
SQL: SELECT `coinname`,`id` FROM `tw_ctmarket` WHERE `status` = 1 AND `coinname` <> 'usdz'  [ RunTime:0.0002s ]
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.

[ 2025-07-31T00:25:28+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.001967s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001303s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001755s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:28+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001524s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000314s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001174s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001606s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:28+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001532s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000311s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001270s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001712s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:28+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001607s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000344s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001321s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001800s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0004s ]

[ 2025-07-31T00:25:28+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001778s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000418s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001509s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002080s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:29+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001505s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000315s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001178s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001609s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:30+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001555s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001190s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001631s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:30+08:00 ] ************** /Ajaxtrade/getallcoin
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001550s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001283s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001724s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0003s ]
SQL: SELECT `coinname`,`id` FROM `tw_ctmarket` WHERE `status` = 1 AND `coinname` <> 'usdz'  [ RunTime:0.0002s ]
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.

[ 2025-07-31T00:25:30+08:00 ] ************** /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001521s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001218s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001658s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:30+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001554s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001219s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001664s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:30+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001631s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001241s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001683s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:30+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001688s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001271s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001723s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:30+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001539s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001356s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001813s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:31+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001530s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001247s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001739s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:31+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001630s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001216s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001656s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:31+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001573s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000342s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001209s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001677s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:32+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001557s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001255s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001698s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:32+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001646s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000334s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001439s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001928s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:32+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001517s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001277s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001725s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:33+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001528s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000315s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001259s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001692s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:33+08:00 ] ************** /Ajaxtrade/getallcoin
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001841s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000484s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002020s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002666s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0004s ]
SQL: SELECT `coinname`,`id` FROM `tw_ctmarket` WHERE `status` = 1 AND `coinname` <> 'usdz'  [ RunTime:0.0002s ]
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.

[ 2025-07-31T00:25:33+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001676s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001231s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001675s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:34+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.002633s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000489s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002053s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002714s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:34+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001554s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001217s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001659s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:34+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001545s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001221s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001667s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:34+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001705s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000371s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001282s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001772s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:34+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001605s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001453s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001903s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:35+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001553s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001217s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001654s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:36+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001534s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000331s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001337s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001786s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:36+08:00 ] ************** /Ajaxtrade/getallcoin
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001606s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000492s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001336s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001959s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0003s ]
SQL: SELECT `coinname`,`id` FROM `tw_ctmarket` WHERE `status` = 1 AND `coinname` <> 'usdz'  [ RunTime:0.0002s ]
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.

[ 2025-07-31T00:25:36+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001547s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001610s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002054s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:36+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001573s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000314s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001238s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001671s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:36+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002749s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000334s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001263s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001731s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:36+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.003324s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000333s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001276s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001736s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:36+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001533s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001202s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001647s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:37+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001538s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001213s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001656s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:38+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001571s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001219s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001660s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:38+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001574s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000353s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001213s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001685s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:38+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001557s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001273s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001712s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:38+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001609s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000340s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001294s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001760s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:39+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001509s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000313s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001206s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001637s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:39+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001546s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000315s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001232s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001673s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:39+08:00 ] ************** /Ajaxtrade/getallcoin
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002149s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001243s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001686s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0003s ]
SQL: SELECT `coinname`,`id` FROM `tw_ctmarket` WHERE `status` = 1 AND `coinname` <> 'usdz'  [ RunTime:0.0002s ]
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.

[ 2025-07-31T00:25:40+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000015s ]
INFO: [ app_init ] --END-- [ RunTime:0.003072s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.003947s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001407s ]
INFO: [ app_begin ] --END-- [ RunTime:0.007263s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:40+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001535s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000329s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001254s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001722s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:40+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001558s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001213s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001655s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:40+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001625s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000450s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001267s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001877s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:40+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001581s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000347s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001258s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001742s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:41+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001577s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001209s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001654s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:42+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001545s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000310s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001275s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001716s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:42+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001701s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000345s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001287s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001770s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:42+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001747s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000333s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001228s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001682s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:42+08:00 ] ************** /Ajaxtrade/getallcoin
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001529s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000350s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001209s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001681s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0004s ]
SQL: SELECT `coinname`,`id` FROM `tw_ctmarket` WHERE `status` = 1 AND `coinname` <> 'usdz'  [ RunTime:0.0002s ]
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.

[ 2025-07-31T00:25:43+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001607s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001236s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001678s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:43+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001528s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001233s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001680s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:43+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001527s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000394s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001287s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001800s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:43+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001618s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000331s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001217s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001665s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:44+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001535s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000316s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001194s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001625s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:45+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001524s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001204s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001641s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:45+08:00 ] ************** /Ajaxtrade/getallcoin
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001527s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000330s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001222s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001672s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0004s ]
SQL: SELECT `coinname`,`id` FROM `tw_ctmarket` WHERE `status` = 1 AND `coinname` <> 'usdz'  [ RunTime:0.0002s ]
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.

[ 2025-07-31T00:25:45+08:00 ] ************** /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001540s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000352s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001284s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001748s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:45+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001579s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000315s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001282s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001714s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:45+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001560s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001259s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001708s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:45+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001552s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001250s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001698s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:45+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002591s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000397s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001377s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001898s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:46+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.002371s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000461s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001975s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002618s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:46+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001587s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001206s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001648s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:46+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001519s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000315s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001202s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001650s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:46+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001559s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000314s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001212s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001644s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:46+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001535s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000310s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001172s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001598s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:46+08:00 ] ************** /Ajaxtrade/obtain_usdz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001603s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001231s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001678s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_market` [ RunTime:0.0004s ]
SQL: SELECT `new_price`,`min_price`,`max_price`,`faxingjia`,`volume` FROM `tw_market` WHERE `name` = 'usdz_usdt' LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:46+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001561s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000333s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001219s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001670s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:48+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001576s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001222s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001662s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:48+08:00 ] ************** /Ajaxtrade/getallcoin
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001553s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000336s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001260s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001717s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0003s ]
SQL: SELECT `coinname`,`id` FROM `tw_ctmarket` WHERE `status` = 1 AND `coinname` <> 'usdz'  [ RunTime:0.0002s ]
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.

[ 2025-07-31T00:25:48+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001695s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000329s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001218s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001667s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:48+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001531s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001332s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001774s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:48+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001564s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001237s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001678s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:48+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001533s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001218s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001656s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:49+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001755s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001474s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001927s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:50+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001519s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000340s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001182s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001644s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:51+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001598s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001202s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001644s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:51+08:00 ] ************** /Ajaxtrade/getallcoin
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001551s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000327s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001203s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001646s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0003s ]
SQL: SELECT `coinname`,`id` FROM `tw_ctmarket` WHERE `status` = 1 AND `coinname` <> 'usdz'  [ RunTime:0.0002s ]
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.

[ 2025-07-31T00:25:51+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001541s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001280s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001721s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:51+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001533s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000310s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001208s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001635s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:51+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001711s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001237s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001676s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:51+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001613s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001960s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002432s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:52+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.001862s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000494s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001631s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002278s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:52+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001560s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001544s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001987s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:52+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001584s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000342s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001258s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001718s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:52+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001501s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000345s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001223s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001686s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:52+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001542s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001242s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001688s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:52+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001591s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001303s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001754s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:53+08:00 ] ************** /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001613s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000330s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001308s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001756s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:54+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001560s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001264s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001704s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:54+08:00 ] ************** /Ajaxtrade/getallcoin
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001643s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001264s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001721s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0003s ]
SQL: SELECT `coinname`,`id` FROM `tw_ctmarket` WHERE `status` = 1 AND `coinname` <> 'usdz'  [ RunTime:0.0002s ]
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.

[ 2025-07-31T00:25:54+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001573s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001247s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001689s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:54+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001569s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001213s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001653s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:54+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001579s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001278s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001720s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:54+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001658s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001215s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001656s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:54+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001697s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000349s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001355s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001850s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:55+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001549s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001280s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001727s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:55+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001539s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001248s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001684s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:56+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001512s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001370s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001809s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:56+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001540s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001170s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001612s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:56+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001541s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000312s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001221s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001650s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:56+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001599s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001197s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001634s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:57+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001715s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000329s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001401s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001926s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:57+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.001849s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000445s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001955s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002544s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:58+08:00 ] ************** /Ajaxtrade/getallcoin
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001603s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001219s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001662s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0003s ]
SQL: SELECT `coinname`,`id` FROM `tw_ctmarket` WHERE `status` = 1 AND `coinname` <> 'usdz'  [ RunTime:0.0002s ]
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.

[ 2025-07-31T00:25:58+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001540s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001272s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001717s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.

[ 2025-07-31T00:25:58+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001738s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000355s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001266s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001741s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:25:58+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001621s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001211s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001659s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:00+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001563s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000344s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001259s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001726s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:01+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001569s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001202s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001643s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:01+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001711s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001262s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001704s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'zDadA' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','4f4eee2ca91ec3bc0611cef71641bf42','5000.00000000','zDadA','163','0','0','2','',',163','**************','非洲','1753892761','1','1')

[ 2025-07-31T00:26:01+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001548s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001265s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001703s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:01+08:00 ] ************** /Home/Autoexe/hycarryout
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001578s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000349s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001251s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001716s ]
SQL: SHOW COLUMNS FROM `tw_hyorder` [ RunTime:0.0006s ]
SQL: SELECT * FROM `tw_hyorder` WHERE `status` = 1 AND `intselltime` <= 1753892761  [ RunTime:0.0006s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0004s ]
SQL: SELECT `hy_ksid`,`hy_ylid`,`hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000368s ]
INFO: [ app_end ] --END-- [ RunTime:0.000434s ]

[ 2025-07-31T00:26:02+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.001638s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001273s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001718s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'ltkLq' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','4f4eee2ca91ec3bc0611cef71641bf42','5000.00000000','ltkLq','163','0','0','2','',',163','**************','非洲','1753892762','1','1')

[ 2025-07-31T00:26:02+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001555s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001299s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001745s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'sNial' LIMIT 1   [ RunTime:0.0003s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','4f4eee2ca91ec3bc0611cef71641bf42','5000.00000000','sNial','163','0','0','2','',',163','**************','非洲','1753892762','1','1')

[ 2025-07-31T00:26:02+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001538s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001206s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001645s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:02+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001611s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001202s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001641s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'nPKxS' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','4f4eee2ca91ec3bc0611cef71641bf42','5000.00000000','nPKxS','163','0','0','2','',',163','**************','非洲','1753892762','1','1')

[ 2025-07-31T00:26:02+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.001808s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000348s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001285s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001768s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'ZdEkB' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','4f4eee2ca91ec3bc0611cef71641bf42','5000.00000000','ZdEkB','163','0','0','2','',',163','**************','非洲','1753892762','1','1')

[ 2025-07-31T00:26:02+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001531s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001317s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001763s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'xfVeo' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0005s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','4f4eee2ca91ec3bc0611cef71641bf42','5000.00000000','xfVeo','163','0','0','2','',',163','**************','非洲','1753892762','1','1')

[ 2025-07-31T00:26:02+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.001563s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000468s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001361s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001985s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0005s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'abwFx' LIMIT 1   [ RunTime:0.0003s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0005s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','4f4eee2ca91ec3bc0611cef71641bf42','5000.00000000','abwFx','163','0','0','2','',',163','**************','非洲','1753892762','1','1')

[ 2025-07-31T00:26:03+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001588s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000354s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001288s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001767s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'uvati' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','4f4eee2ca91ec3bc0611cef71641bf42','5000.00000000','uvati','163','0','0','2','',',163','**************','非洲','1753892763','1','1')

[ 2025-07-31T00:26:03+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001561s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000402s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001245s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001777s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'NJzVE' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','4f4eee2ca91ec3bc0611cef71641bf42','5000.00000000','NJzVE','163','0','0','2','',',163','**************','非洲','1753892763','1','1')

[ 2025-07-31T00:26:03+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001593s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000339s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001250s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001710s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:03+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001544s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000316s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001217s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001649s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:03+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001545s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000341s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001239s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001700s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'AsNxh' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','4f4eee2ca91ec3bc0611cef71641bf42','5000.00000000','AsNxh','163','0','0','2','',',163','**************','非洲','1753892763','1','1')

[ 2025-07-31T00:26:03+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.001527s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000327s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001220s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001665s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'bZQEM' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','4f4eee2ca91ec3bc0611cef71641bf42','5000.00000000','bZQEM','163','0','0','2','',',163','**************','非洲','1753892763','1','1')

[ 2025-07-31T00:26:03+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001537s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001228s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001664s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'UGkRj' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','4f4eee2ca91ec3bc0611cef71641bf42','5000.00000000','UGkRj','163','0','0','2','',',163','**************','非洲','1753892763','1','1')

[ 2025-07-31T00:26:04+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002484s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000368s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001282s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001776s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:05+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001669s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000365s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001234s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001720s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:06+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001675s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001195s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001637s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:07+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001569s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000315s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001197s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001636s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:07+08:00 ] ************** /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001571s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001195s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001640s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:07+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001564s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000315s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001216s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001646s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:07+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001570s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000327s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.008080s ]
INFO: [ app_begin ] --END-- [ RunTime:0.008545s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:07+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001560s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001259s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001720s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:07+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001532s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001323s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001790s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:07+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001626s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001237s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001674s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:09+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001598s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000468s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001199s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001803s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:09+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.002714s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000489s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001942s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002593s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:10+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001528s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000335s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001337s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001790s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:11+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001555s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000316s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001329s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001765s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:11+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001613s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001255s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001696s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:11+08:00 ] ************** /Ajaxtrade/obtain_usdz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001528s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000371s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001318s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001825s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_market` [ RunTime:0.0004s ]
SQL: SELECT `new_price`,`min_price`,`max_price`,`faxingjia`,`volume` FROM `tw_market` WHERE `name` = 'usdz_usdt' LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:12+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001524s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001180s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001632s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:12+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001530s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000345s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001180s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001643s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:13+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001529s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001305s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001742s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-31T00:26:13+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001560s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000313s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001204s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001634s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:14+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001876s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000314s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001217s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001655s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:15+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001544s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001280s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001725s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:15+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001668s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000316s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001238s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001674s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:16+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001552s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000424s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001355s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001908s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:16+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.007093s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000502s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002694s ]
INFO: [ app_begin ] --END-- [ RunTime:0.003396s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:16+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001577s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000334s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001224s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001675s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:16+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001656s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000365s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001238s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001722s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'AJZEY' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','4f4eee2ca91ec3bc0611cef71641bf42','5000.00000000','AJZEY','163','0','0','2','',',163','**************','非洲','1753892776','1','1')

[ 2025-07-31T00:26:16+08:00 ] ************** /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001563s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000329s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001410s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001899s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:17+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001592s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001227s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001676s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'HjTZL' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','4f4eee2ca91ec3bc0611cef71641bf42','5000.00000000','HjTZL','163','0','0','2','',',163','**************','非洲','1753892777','1','1')

[ 2025-07-31T00:26:17+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001714s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000383s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001492s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001998s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'jYVxR' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0005s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','4f4eee2ca91ec3bc0611cef71641bf42','5000.00000000','jYVxR','163','0','0','2','',',163','**************','非洲','1753892777','1','1')

[ 2025-07-31T00:26:17+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001559s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001227s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001667s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'QtHJP' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','4f4eee2ca91ec3bc0611cef71641bf42','5000.00000000','QtHJP','163','0','0','2','',',163','**************','非洲','1753892777','1','1')

[ 2025-07-31T00:26:17+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001651s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001220s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001659s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'hVyQP' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','4f4eee2ca91ec3bc0611cef71641bf42','5000.00000000','hVyQP','163','0','0','2','',',163','**************','非洲','1753892777','1','1')

[ 2025-07-31T00:26:17+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001533s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000361s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001198s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001697s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'anGwX' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','4f4eee2ca91ec3bc0611cef71641bf42','5000.00000000','anGwX','163','0','0','2','',',163','**************','非洲','1753892777','1','1')

[ 2025-07-31T00:26:17+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001607s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001270s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001722s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'YibJq' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','4f4eee2ca91ec3bc0611cef71641bf42','5000.00000000','YibJq','163','0','0','2','',',163','**************','非洲','1753892777','1','1')

[ 2025-07-31T00:26:18+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001590s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000329s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001519s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001999s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'uniCL' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','4f4eee2ca91ec3bc0611cef71641bf42','5000.00000000','uniCL','163','0','0','2','',',163','**************','非洲','1753892778','1','1')

[ 2025-07-31T00:26:18+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001605s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001314s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001759s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'kmIlF' LIMIT 1   [ RunTime:0.0004s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','4f4eee2ca91ec3bc0611cef71641bf42','5000.00000000','kmIlF','163','0','0','2','',',163','**************','非洲','1753892778','1','1')

[ 2025-07-31T00:26:18+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.002594s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000338s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001222s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001678s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'sreUn' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','4f4eee2ca91ec3bc0611cef71641bf42','5000.00000000','sreUn','163','0','0','2','',',163','**************','非洲','1753892778','1','1')

[ 2025-07-31T00:26:18+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001548s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001224s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001664s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:18+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001580s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001306s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001756s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0005s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'QyZFi' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','4f4eee2ca91ec3bc0611cef71641bf42','5000.00000000','QyZFi','163','0','0','2','',',163','**************','非洲','1753892778','1','1')

[ 2025-07-31T00:26:18+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001729s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000344s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001227s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001691s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:18+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001990s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000414s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001283s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001835s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'MNTFp' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','4f4eee2ca91ec3bc0611cef71641bf42','5000.00000000','MNTFp','163','0','0','2','',',163','**************','非洲','1753892778','1','1')

[ 2025-07-31T00:26:18+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001569s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000355s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001218s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001695s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'ExjFd' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','4f4eee2ca91ec3bc0611cef71641bf42','5000.00000000','ExjFd','163','0','0','2','',',163','**************','非洲','1753892778','1','1')

[ 2025-07-31T00:26:19+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001567s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000359s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001249s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001729s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0005s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'pTgMf' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','4f4eee2ca91ec3bc0611cef71641bf42','5000.00000000','pTgMf','163','0','0','2','',',163','**************','非洲','1753892779','1','1')

[ 2025-07-31T00:26:19+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001527s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001227s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001662s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0032s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'loShv' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','4f4eee2ca91ec3bc0611cef71641bf42','5000.00000000','loShv','163','0','0','2','',',163','**************','非洲','1753892779','1','1')

[ 2025-07-31T00:26:19+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001569s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001340s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001784s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'EYZNJAGQU' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'hvwEF' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','ab4c279b477e8e1c90ead99c71c80a4f','5000.00000000','hvwEF','164','161','0','2','',',161,164','**************','日本','1753892779','1','1')

[ 2025-07-31T00:26:19+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001533s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001203s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001652s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:20+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001558s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001203s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001644s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:20+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001662s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001219s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001655s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:21+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001950s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000346s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001221s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001690s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:21+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001607s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000330s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001268s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001738s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:21+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001677s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000359s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001259s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001743s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:21+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.001532s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001325s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001767s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:22+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001571s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000346s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001272s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001737s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:24+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001523s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001204s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001645s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:24+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001532s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001220s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001658s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:24+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001666s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001205s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001663s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:26+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001553s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001253s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001689s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:26+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001546s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000338s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001216s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001677s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:27+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001557s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001276s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001736s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:27+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001562s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000315s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001287s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001726s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:27+08:00 ] ************** /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001564s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000333s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001310s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001763s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:27+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.002220s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001189s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001624s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:28+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001556s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001183s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001635s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:30+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001567s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001213s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001649s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:30+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001562s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001231s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001665s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:31+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001541s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000316s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001234s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001669s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:32+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001546s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000316s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001195s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001620s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:32+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001535s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001232s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001674s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:33+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001555s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000399s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001520s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002062s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:33+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001576s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001195s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001630s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:34+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001555s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001174s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001608s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:34+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001653s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001190s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001627s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:34+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001586s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001201s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001651s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:36+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001517s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000349s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001300s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001768s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:36+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001606s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000351s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001411s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001887s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:36+08:00 ] ************** /Ajaxtrade/obtain_usdz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.005619s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000392s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001249s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001782s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_market` [ RunTime:0.0005s ]
SQL: SELECT `new_price`,`min_price`,`max_price`,`faxingjia`,`volume` FROM `tw_market` WHERE `name` = 'usdz_usdt' LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:36+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.001560s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000361s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001307s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001787s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:36+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001642s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001242s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001686s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:37+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001530s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001265s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001700s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:38+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001586s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001245s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001684s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:38+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001538s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000333s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001199s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001648s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:39+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001535s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001260s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001843s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:39+08:00 ] ************** /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001634s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001258s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001700s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:40+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.002059s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000347s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001243s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001715s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:41+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001571s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000315s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001201s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001640s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:41+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001594s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000315s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001206s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001649s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:42+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001501s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000335s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001253s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001718s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:42+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001570s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001195s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002030s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:42+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001606s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000315s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001190s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001623s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:43+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001518s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001273s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001704s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'EYZNJAGQU' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'QexrN' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','ab4c279b477e8e1c90ead99c71c80a4f','5000.00000000','QexrN','164','161','0','2','',',161,164','**************','日本','1753892803','1','1')

[ 2025-07-31T00:26:44+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002161s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001228s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001680s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:45+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001639s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001476s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001925s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0030s ]

[ 2025-07-31T00:26:45+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001557s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000339s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001178s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001637s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:26:45+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001719s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000729s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001282s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002141s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

