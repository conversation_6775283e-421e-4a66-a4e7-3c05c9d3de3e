[ 2025-07-31T00:00:00+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001532s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000333s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001305s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001758s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'XGVFDNEWQ' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'JjwLl' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','5412e09eb7323baf6546a064292616eb','5000.00000000','JjwLl','165','0','0','2','',',165','**************','非洲','1753891200','1','1')

[ 2025-07-31T00:00:01+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.001718s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000347s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001314s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001782s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'XGVFDNEWQ' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'tFmap' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','5412e09eb7323baf6546a064292616eb','5000.00000000','tFmap','165','0','0','2','',',165','**************','非洲','1753891201','1','1')

[ 2025-07-31T00:00:01+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001651s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000372s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001482s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001987s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'XGVFDNEWQ' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'fwgDU' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','5412e09eb7323baf6546a064292616eb','5000.00000000','fwgDU','165','0','0','2','',',165','**************','非洲','1753891201','1','1')

[ 2025-07-31T00:00:01+08:00 ] ************** /Home/Autoexe/hycarryout
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001582s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000371s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001272s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001756s ]
SQL: SHOW COLUMNS FROM `tw_hyorder` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_hyorder` WHERE `status` = 1 AND `intselltime` <= 1753891201  [ RunTime:0.0004s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0003s ]
SQL: SELECT `hy_ksid`,`hy_ylid`,`hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000374s ]
INFO: [ app_end ] --END-- [ RunTime:0.000439s ]

[ 2025-07-31T00:00:01+08:00 ] ************** /Home/Autoexe/hycarryout
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001667s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000333s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001247s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001688s ]
SQL: SHOW COLUMNS FROM `tw_hyorder` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_hyorder` WHERE `status` = 1 AND `intselltime` <= 1753891201  [ RunTime:0.0004s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0003s ]
SQL: SELECT `hy_ksid`,`hy_ylid`,`hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000361s ]
INFO: [ app_end ] --END-- [ RunTime:0.000439s ]

[ 2025-07-31T00:00:01+08:00 ] ************** /Home/Autoexe/autoxjtade
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001778s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000330s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001345s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001800s ]
SQL: SHOW COLUMNS FROM `tw_bborder` [ RunTime:0.0006s ]
SQL: SELECT * FROM `tw_bborder` WHERE `ordertype` = 1 AND `status` = 1  [ RunTime:0.0003s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000366s ]
INFO: [ app_end ] --END-- [ RunTime:0.000433s ]

[ 2025-07-31T00:00:01+08:00 ] ************** /Home/Autoexe/hycarryout_ty
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001556s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001201s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001636s ]
SQL: SHOW COLUMNS FROM `tw_tyhyorder` [ RunTime:0.0007s ]
SQL: SELECT * FROM `tw_tyhyorder` WHERE `status` = 1 AND `intselltime` <= 1753891201  [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0004s ]
SQL: SELECT `hy_ksid`,`hy_ylid`,`hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000663s ]
INFO: [ app_end ] --END-- [ RunTime:0.000773s ]

[ 2025-07-31T00:00:01+08:00 ] ************** /Home/Autoexe/setwl_ty
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000015s ]
INFO: [ app_init ] --END-- [ RunTime:0.002569s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000341s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001279s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001741s ]
SQL: SHOW COLUMNS FROM `tw_tyhyorder` [ RunTime:0.0004s ]
SQL: SELECT COUNT(*) AS tp_count FROM `tw_tyhyorder` WHERE `status` = 1 AND `kongyk` = 0 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0003s ]
SQL: SELECT `hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000485s ]
INFO: [ app_end ] --END-- [ RunTime:0.000582s ]

[ 2025-07-31T00:00:02+08:00 ] ************** /Home/Autoexe/setwl
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001478s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001190s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001633s ]
SQL: SHOW COLUMNS FROM `tw_hyorder` [ RunTime:0.0004s ]
SQL: SELECT COUNT(*) AS tp_count FROM `tw_hyorder` WHERE `status` = 1 AND `kongyk` = 0 LIMIT 1   [ RunTime:0.0004s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0003s ]
SQL: SELECT `hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000364s ]
INFO: [ app_end ] --END-- [ RunTime:0.000428s ]

[ 2025-07-31T00:00:02+08:00 ] ************** /Home/Autoexe/hycarryout_ty
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001506s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001191s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001631s ]
SQL: SHOW COLUMNS FROM `tw_tyhyorder` [ RunTime:0.0005s ]
SQL: SELECT * FROM `tw_tyhyorder` WHERE `status` = 1 AND `intselltime` <= 1753891202  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0003s ]
SQL: SELECT `hy_ksid`,`hy_ylid`,`hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000387s ]
INFO: [ app_end ] --END-- [ RunTime:0.000467s ]

[ 2025-07-31T00:00:04+08:00 ] ************** /Home/Autoexe/setwl
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001545s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001257s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001747s ]
SQL: SHOW COLUMNS FROM `tw_hyorder` [ RunTime:0.0005s ]
SQL: SELECT COUNT(*) AS tp_count FROM `tw_hyorder` WHERE `status` = 1 AND `kongyk` = 0 LIMIT 1   [ RunTime:0.0004s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0003s ]
SQL: SELECT `hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000381s ]
INFO: [ app_end ] --END-- [ RunTime:0.000445s ]

[ 2025-07-31T00:00:05+08:00 ] ************** /Home/Autoexe/setwl_ty
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001489s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000340s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001214s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001665s ]
SQL: SHOW COLUMNS FROM `tw_tyhyorder` [ RunTime:0.0005s ]
SQL: SELECT COUNT(*) AS tp_count FROM `tw_tyhyorder` WHERE `status` = 1 AND `kongyk` = 0 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0003s ]
SQL: SELECT `hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000408s ]
INFO: [ app_end ] --END-- [ RunTime:0.000473s ]

[ 2025-07-31T00:00:07+08:00 ] ************** /Home/Autoexe/autoxjtade
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001569s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001283s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001713s ]
SQL: SHOW COLUMNS FROM `tw_bborder` [ RunTime:0.0005s ]
SQL: SELECT * FROM `tw_bborder` WHERE `ordertype` = 1 AND `status` = 1  [ RunTime:0.0003s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000374s ]
INFO: [ app_end ] --END-- [ RunTime:0.000437s ]

[ 2025-07-31T00:00:09+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001643s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001273s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001714s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0010s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'XGVFDNEWQ' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'zZoQP' LIMIT 1   [ RunTime:0.0003s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0005s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','eef3778cb8a54d2505be78137ad6fc37','5000.00000000','zZoQP','165','0','0','2','',',165','**************','非洲','1753891209','1','1')

[ 2025-07-31T00:00:13+08:00 ] ************** /Login/register/Lang/zh-cn
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001622s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000342s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001306s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001769s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000068s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000143s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8] Undefined variable: uid /www/wwwroot/www.3728.ltd/Runtime/Cache/Home/524a08806bc29449645d93a7a8f8e680.php 第 237 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8] Undefined variable: qrcode /www/wwwroot/www.3728.ltd/Runtime/Cache/Home/524a08806bc29449645d93a7a8f8e680.php 第 557 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.010275s ]
INFO: [ view_parse ] --END-- [ RunTime:0.010341s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000181s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000236s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000350s ]
INFO: [ app_end ] --END-- [ RunTime:0.000419s ]

[ 2025-07-31T00:00:22+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001558s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000344s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001339s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001804s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'JiLSg' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','eef3778cb8a54d2505be78137ad6fc37','5000.00000000','JiLSg','163','0','0','2','',',163','**************','非洲','1753891222','1','1')

[ 2025-07-31T00:00:23+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001789s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000381s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001430s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001959s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0005s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'rWSjv' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0009s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','eef3778cb8a54d2505be78137ad6fc37','5000.00000000','rWSjv','163','0','0','2','',',163','**************','非洲','1753891223','1','1')

[ 2025-07-31T00:00:23+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001846s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000517s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001823s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002512s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'NmnyA' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','eef3778cb8a54d2505be78137ad6fc37','5000.00000000','NmnyA','163','0','0','2','',',163','**************','非洲','1753891223','1','1')

[ 2025-07-31T00:00:24+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.002018s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001342s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001787s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'Zrgtk' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','eef3778cb8a54d2505be78137ad6fc37','5000.00000000','Zrgtk','163','0','0','2','',',163','**************','非洲','1753891224','1','1')

[ 2025-07-31T00:00:24+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001613s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001256s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001695s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'xiLQa' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','eef3778cb8a54d2505be78137ad6fc37','5000.00000000','xiLQa','163','0','0','2','',',163','**************','非洲','1753891224','1','1')

[ 2025-07-31T00:00:24+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001514s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000337s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001248s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001704s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0005s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'Rimyh' LIMIT 1   [ RunTime:0.0003s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0006s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','eef3778cb8a54d2505be78137ad6fc37','5000.00000000','Rimyh','163','0','0','2','',',163','**************','非洲','1753891224','1','1')

[ 2025-07-31T00:00:24+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001529s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000327s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001285s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001732s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'frGqk' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','eef3778cb8a54d2505be78137ad6fc37','5000.00000000','frGqk','163','0','0','2','',',163','**************','非洲','1753891224','1','1')

[ 2025-07-31T00:00:24+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001800s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000335s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001407s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001865s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0005s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'uidcb' LIMIT 1   [ RunTime:0.0003s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','eef3778cb8a54d2505be78137ad6fc37','5000.00000000','uidcb','163','0','0','2','',',163','**************','非洲','1753891224','1','1')

[ 2025-07-31T00:00:24+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.002258s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000334s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001342s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001816s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'kRgjo' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','eef3778cb8a54d2505be78137ad6fc37','5000.00000000','kRgjo','163','0','0','2','',',163','**************','非洲','1753891224','1','1')

[ 2025-07-31T00:00:24+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001543s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000337s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001279s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001769s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'GVIKk' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','eef3778cb8a54d2505be78137ad6fc37','5000.00000000','GVIKk','163','0','0','2','',',163','**************','非洲','1753891224','1','1')

[ 2025-07-31T00:00:25+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001753s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001272s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001732s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'LEhMd' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','eef3778cb8a54d2505be78137ad6fc37','5000.00000000','LEhMd','163','0','0','2','',',163','**************','非洲','1753891225','1','1')

[ 2025-07-31T00:00:25+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001533s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000315s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001252s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001680s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'Shwpf' LIMIT 1   [ RunTime:0.0003s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','eef3778cb8a54d2505be78137ad6fc37','5000.00000000','Shwpf','163','0','0','2','',',163','**************','非洲','1753891225','1','1')

[ 2025-07-31T00:00:25+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001640s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000333s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001287s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001740s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'GYeya' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','eef3778cb8a54d2505be78137ad6fc37','5000.00000000','GYeya','163','0','0','2','',',163','**************','非洲','1753891225','1','1')

[ 2025-07-31T00:00:25+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001558s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001263s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001706s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0005s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'FhmXj' LIMIT 1   [ RunTime:0.0003s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0005s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','eef3778cb8a54d2505be78137ad6fc37','5000.00000000','FhmXj','163','0','0','2','',',163','**************','非洲','1753891225','1','1')

[ 2025-07-31T00:00:25+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001628s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000522s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002157s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002851s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0005s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'VhTLg' LIMIT 1   [ RunTime:0.0004s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','eef3778cb8a54d2505be78137ad6fc37','5000.00000000','VhTLg','163','0','0','2','',',163','**************','非洲','1753891225','1','1')

[ 2025-07-31T00:00:28+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001574s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000358s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001213s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001696s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-31T00:00:28+08:00 ] ************** /Ajaxtrade/obtain_usdz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001517s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001209s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001649s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_market` [ RunTime:0.0005s ]
SQL: SELECT `new_price`,`min_price`,`max_price`,`faxingjia`,`volume` FROM `tw_market` WHERE `name` = 'usdz_usdt' LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:28+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001784s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000473s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001963s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002600s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:28+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001562s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001220s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001654s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:28+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002098s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000333s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001280s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001733s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:28+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001586s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001434s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001898s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:28+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000015s ]
INFO: [ app_init ] --END-- [ RunTime:0.001706s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000345s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002107s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002567s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:28+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001663s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001384s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001831s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:29+08:00 ] ************** /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001536s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000330s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001193s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001641s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0013s ]

[ 2025-07-31T00:00:29+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.005035s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001837s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002279s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:29+08:00 ] ************** /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001701s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000331s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001832s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002284s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:29+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001549s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001264s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001707s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:29+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001856s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000348s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001202s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001701s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:29+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001518s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000313s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001202s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001633s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:30+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001526s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000339s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001206s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001663s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:30+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001636s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001179s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001614s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:31+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002431s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000442s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001598s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002201s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:32+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001516s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001196s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001635s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:32+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.002734s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000314s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001569s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002004s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:33+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001520s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000439s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001211s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001778s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:33+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001561s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000346s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001200s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001668s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:33+08:00 ] ************** /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001613s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001275s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001730s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:34+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001550s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000333s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001511s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001963s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:34+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000024s ]
INFO: [ app_init ] --END-- [ RunTime:0.001639s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001291s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001751s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:34+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001606s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000354s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001239s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001710s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:35+08:00 ] ************** /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001562s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000340s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001195s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001682s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:36+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.001507s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001342s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001788s ]

[ 2025-07-31T00:00:36+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001537s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000338s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001324s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001786s ]

[ 2025-07-31T00:00:36+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.003078s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000486s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002037s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002692s ]

[ 2025-07-31T00:00:36+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001550s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000561s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001206s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001913s ]

[ 2025-07-31T00:00:36+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001541s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001197s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001648s ]

[ 2025-07-31T00:00:36+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001991s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000372s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001578s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002080s ]

[ 2025-07-31T00:00:38+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001591s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000345s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001230s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001693s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0046s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-31T00:00:39+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001651s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000345s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001195s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001659s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:40+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001564s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000331s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001258s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001705s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:40+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001574s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001194s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001630s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:41+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001518s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000357s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001177s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001653s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:44+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001870s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000347s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001321s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001795s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:45+08:00 ] ************** /Login/register/Lang/zh-cn
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001743s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000339s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001400s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001869s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000074s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000147s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8] Undefined variable: uid /www/wwwroot/www.3728.ltd/Runtime/Cache/Home/524a08806bc29449645d93a7a8f8e680.php 第 237 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8] Undefined variable: qrcode /www/wwwroot/www.3728.ltd/Runtime/Cache/Home/524a08806bc29449645d93a7a8f8e680.php 第 557 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.010325s ]
INFO: [ view_parse ] --END-- [ RunTime:0.010396s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000197s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000253s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000417s ]
INFO: [ app_end ] --END-- [ RunTime:0.000500s ]

[ 2025-07-31T00:00:45+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001826s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000386s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001390s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001908s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:45+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001972s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000341s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001405s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001899s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:46+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001710s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000339s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001359s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001829s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:46+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001858s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000398s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001438s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001978s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:46+08:00 ] ************** /Ajaxtrade/obtain_usdz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001820s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000371s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001514s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002015s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_market` [ RunTime:0.0013s ]
SQL: SELECT `new_price`,`min_price`,`max_price`,`faxingjia`,`volume` FROM `tw_market` WHERE `name` = 'usdz_usdt' LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:47+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001670s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001283s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001732s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:47+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001752s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000359s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001333s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001819s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:47+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001783s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000365s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001355s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001851s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:48+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001671s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000419s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001390s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001938s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:48+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001770s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000390s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001408s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001949s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:49+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001773s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000342s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001310s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001796s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:50+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001638s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001364s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001820s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:51+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001678s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000334s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001276s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001735s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:51+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001655s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000330s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001284s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001738s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:51+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001700s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000335s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001289s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001752s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:52+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001661s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000329s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001274s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001725s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:53+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001694s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000355s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001414s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001898s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:54+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001696s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000355s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001335s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001821s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:54+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.001532s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000349s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001265s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001732s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:54+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001593s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000408s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001237s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001805s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:54+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001584s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001255s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001696s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:54+08:00 ] ************** /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001586s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001293s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001738s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:56+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001590s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000340s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001199s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001655s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:56+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001538s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001249s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001683s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:57+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001588s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001291s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001748s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:58+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001616s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000331s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001375s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001849s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0007s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'eRksx' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0004s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0005s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','2abfb657331f2f30809e272cba829056','5000.00000000','eRksx','163','0','0','2','',',163','**************','非洲','1753891258','1','1')

[ 2025-07-31T00:00:58+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001546s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001241s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001682s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:58+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001662s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001340s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001783s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:58+08:00 ] ************** /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001683s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000343s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001301s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001764s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:00:59+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001497s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001274s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001709s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'VZQom' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0005s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','2abfb657331f2f30809e272cba829056','5000.00000000','VZQom','163','0','0','2','',',163','**************','非洲','1753891259','1','1')

[ 2025-07-31T00:00:59+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001554s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000371s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001311s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001803s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0005s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'ZUiwc' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0005s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','2abfb657331f2f30809e272cba829056','5000.00000000','ZUiwc','163','0','0','2','',',163','**************','非洲','1753891259','1','1')

[ 2025-07-31T00:00:59+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001593s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000327s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001287s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001737s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'RGyVh' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0005s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','2abfb657331f2f30809e272cba829056','5000.00000000','RGyVh','163','0','0','2','',',163','**************','非洲','1753891259','1','1')

[ 2025-07-31T00:00:59+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001558s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000357s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001290s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001769s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'mNRIp' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0005s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','2abfb657331f2f30809e272cba829056','5000.00000000','mNRIp','163','0','0','2','',',163','**************','非洲','1753891259','1','1')

[ 2025-07-31T00:00:59+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001655s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000339s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001322s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001786s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'IqQMm' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0005s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','2abfb657331f2f30809e272cba829056','5000.00000000','IqQMm','163','0','0','2','',',163','**************','非洲','1753891259','1','1')

[ 2025-07-31T00:00:59+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001533s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001292s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001760s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'JTrwm' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0005s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','2abfb657331f2f30809e272cba829056','5000.00000000','JTrwm','163','0','0','2','',',163','**************','非洲','1753891259','1','1')

[ 2025-07-31T00:01:00+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001599s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001188s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001640s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0030s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:00+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001560s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000335s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001232s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001687s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:00+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001573s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001361s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001832s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:01+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001850s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000348s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001297s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001766s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:01+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.001865s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001365s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001815s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:01+08:00 ] ************** /Home/Autoexe/hycarryout
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001717s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000386s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001364s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001882s ]
SQL: SHOW COLUMNS FROM `tw_hyorder` [ RunTime:0.0007s ]
SQL: SELECT * FROM `tw_hyorder` WHERE `status` = 1 AND `intselltime` <= 1753891261  [ RunTime:0.0004s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0004s ]
SQL: SELECT `hy_ksid`,`hy_ylid`,`hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000453s ]
INFO: [ app_end ] --END-- [ RunTime:0.000522s ]

[ 2025-07-31T00:01:03+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.002123s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001211s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001657s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:03+08:00 ] ************** /Public/Dela/template/dela-template.css.map
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001605s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000334s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001316s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001786s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.

[ 2025-07-31T00:01:04+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001699s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000342s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001917s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002416s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:04+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001712s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000397s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001266s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001804s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-31T00:01:05+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001582s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000316s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001249s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001683s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:07+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001526s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001232s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001668s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:07+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001608s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001241s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001692s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:07+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.002233s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000432s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001301s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001912s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:07+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001609s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000331s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001297s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001759s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:07+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001678s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000410s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001453s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002003s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:09+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001616s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001232s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001669s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:09+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001616s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001265s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001709s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:10+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001653s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001220s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001666s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:10+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001727s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001231s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001669s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:11+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001657s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001244s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001683s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:11+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001553s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001401s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001849s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:12+08:00 ] ************** /Ajaxtrade/obtain_usdz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001585s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001200s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001637s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_market` [ RunTime:0.0005s ]
SQL: SELECT `new_price`,`min_price`,`max_price`,`faxingjia`,`volume` FROM `tw_market` WHERE `name` = 'usdz_usdt' LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:12+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001733s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000347s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001346s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001821s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:13+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001622s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001232s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001672s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:13+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001594s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000405s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001426s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001971s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:13+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001525s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000432s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001719s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002273s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:13+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001711s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000382s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001482s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001991s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:14+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001539s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000357s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001232s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001711s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:15+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001629s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000335s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001289s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001746s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:16+08:00 ] ************** /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001569s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000313s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001266s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001709s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:16+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001567s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000330s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001308s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001756s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:16+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001741s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000349s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001317s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001796s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:17+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001537s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001253s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001691s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:17+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001975s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000345s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001350s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001822s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:17+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000017s ]
INFO: [ app_init ] --END-- [ RunTime:0.001590s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001256s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001698s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'bGeIc' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0005s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','2abfb657331f2f30809e272cba829056','5000.00000000','bGeIc','163','0','0','2','',',163','**************','非洲','1753891277','1','1')

[ 2025-07-31T00:01:18+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.002417s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000342s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001375s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001839s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'DGUjt' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0005s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','2abfb657331f2f30809e272cba829056','5000.00000000','DGUjt','163','0','0','2','',',163','**************','非洲','1753891278','1','1')

[ 2025-07-31T00:01:18+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001861s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000395s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001468s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002002s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'qgEhB' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0005s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','2abfb657331f2f30809e272cba829056','5000.00000000','qgEhB','163','0','0','2','',',163','**************','非洲','1753891278','1','1')

[ 2025-07-31T00:01:18+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001578s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000351s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001275s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001756s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'RGSzp' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','2abfb657331f2f30809e272cba829056','5000.00000000','RGSzp','163','0','0','2','',',163','**************','非洲','1753891278','1','1')

[ 2025-07-31T00:01:18+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001518s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000315s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001270s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001705s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'EDtUj' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0006s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','2abfb657331f2f30809e272cba829056','5000.00000000','EDtUj','163','0','0','2','',',163','**************','非洲','1753891278','1','1')

[ 2025-07-31T00:01:19+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001558s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001342s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001780s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'WhCTG' LIMIT 1   [ RunTime:0.0005s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0005s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','2abfb657331f2f30809e272cba829056','5000.00000000','WhCTG','163','0','0','2','',',163','**************','非洲','1753891279','1','1')

[ 2025-07-31T00:01:19+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001577s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000340s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001216s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001674s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:19+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001579s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001222s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001664s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:21+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001723s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001296s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001738s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:21+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001604s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000352s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001242s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001714s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:22+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001758s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001215s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001672s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:22+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001603s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000342s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001258s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001721s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:22+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001567s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000334s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001217s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001669s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:22+08:00 ] ************** /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001628s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000331s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001438s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002696s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:22+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001596s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000346s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001357s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001823s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:22+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001638s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000343s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001189s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001651s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:22+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001713s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001337s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001780s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:23+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001694s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000333s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001320s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001791s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:24+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001554s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001216s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001654s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:24+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001562s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001286s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001730s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:26+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001577s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000362s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001366s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001869s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:26+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001565s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000331s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001271s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001725s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:26+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002132s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000386s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001516s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002032s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:27+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001625s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000313s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001218s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001650s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:28+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001624s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000348s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001230s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001701s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:29+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001582s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000327s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001203s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001651s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:30+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001547s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001231s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001667s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:30+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002155s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000354s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001256s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001768s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:30+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001618s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000329s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001301s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001748s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:31+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001675s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000338s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001292s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001755s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:31+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001871s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000368s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001385s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001881s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:31+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002580s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000400s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001461s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002018s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:32+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001619s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000569s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001247s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001936s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-31T00:01:33+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001726s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000438s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001402s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001978s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:34+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002174s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000415s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001565s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002131s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:36+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002100s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000355s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001646s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002159s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:36+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001947s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001232s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001669s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:36+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000015s ]
INFO: [ app_init ] --END-- [ RunTime:0.001549s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000358s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001268s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001744s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:36+08:00 ] ************** /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001534s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000330s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001362s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001813s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:36+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001640s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001266s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001718s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:37+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001563s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001269s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001711s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:37+08:00 ] ************** /Ajaxtrade/obtain_usdz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002213s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000343s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001873s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002358s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_market` [ RunTime:0.0005s ]
SQL: SELECT `new_price`,`min_price`,`max_price`,`faxingjia`,`volume` FROM `tw_market` WHERE `name` = 'usdz_usdt' LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:38+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001631s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000370s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001369s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001863s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:39+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001540s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000334s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001191s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001644s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:39+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001535s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000330s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001214s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001663s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:39+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001560s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001273s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001726s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:40+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000026s ]
INFO: [ app_init ] --END-- [ RunTime:0.001599s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001184s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001623s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:40+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001833s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000365s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001263s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001749s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:41+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001518s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000338s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001194s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001651s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:42+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001521s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000345s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001203s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001667s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:42+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001642s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000352s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001328s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001800s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:42+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002321s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000465s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002065s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002693s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:43+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001583s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000335s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001364s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001850s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:44+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001521s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000314s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001176s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001608s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:44+08:00 ] ************** /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001813s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000330s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001289s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001740s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:45+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000015s ]
INFO: [ app_init ] --END-- [ RunTime:0.001582s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001185s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001624s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:46+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001536s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000311s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001310s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001740s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:46+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.001722s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000356s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001365s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001880s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:48+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001537s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000310s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001188s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001613s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:48+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001544s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000488s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001454s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002104s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:48+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001560s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000344s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001355s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001823s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:50+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001787s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000417s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001576s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002128s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:50+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001515s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001177s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001610s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:50+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001555s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000314s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001297s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001734s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:51+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001524s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001227s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001666s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:51+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001571s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001235s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001680s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:51+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001580s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001254s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001705s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:52+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001568s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000342s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001211s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001670s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:52+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001563s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000327s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001243s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001689s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:53+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001726s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000481s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001984s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002635s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:54+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001558s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000316s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001219s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001660s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:54+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001678s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001276s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001723s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:56+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001548s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000327s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001212s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001664s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:56+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001643s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001210s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001648s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:57+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001550s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001245s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001692s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:57+08:00 ] ************** /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001557s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000355s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001340s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001826s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:57+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001569s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000331s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001207s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001656s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:57+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001592s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000503s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002091s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002787s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:57+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001642s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000335s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001266s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001729s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:01:58+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001524s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001238s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001684s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:00+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001555s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000329s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001217s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001669s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:00+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001589s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000316s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001238s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001681s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:01+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001535s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001200s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001643s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:01+08:00 ] ************** /Ajaxtrade/obtain_usdz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001530s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001342s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001785s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_market` [ RunTime:0.0004s ]
SQL: SELECT `new_price`,`min_price`,`max_price`,`faxingjia`,`volume` FROM `tw_market` WHERE `name` = 'usdz_usdt' LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:02+08:00 ] ************** /Home/Autoexe/setwl
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001595s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001209s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001640s ]
SQL: SHOW COLUMNS FROM `tw_hyorder` [ RunTime:0.0007s ]
SQL: SELECT COUNT(*) AS tp_count FROM `tw_hyorder` WHERE `status` = 1 AND `kongyk` = 0 LIMIT 1   [ RunTime:0.0006s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0005s ]
SQL: SELECT `hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000356s ]
INFO: [ app_end ] --END-- [ RunTime:0.000419s ]

[ 2025-07-31T00:02:02+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001554s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000315s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001217s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001649s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:03+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001755s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000459s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001351s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001934s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:04+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001607s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000361s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001289s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001771s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:04+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001931s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000339s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001341s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001810s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:04+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000003s ]
INFO: [ app_init ] --END-- [ RunTime:0.001516s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000329s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001325s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001793s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:05+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001621s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000336s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001280s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001737s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-31T00:02:06+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001623s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000391s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001222s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001733s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:06+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001567s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000334s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001212s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001663s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:06+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001765s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000417s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001387s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001939s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:06+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001551s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000352s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001276s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001747s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:06+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000005s ]
INFO: [ app_init ] --END-- [ RunTime:0.001553s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001233s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001673s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:07+08:00 ] ************** /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001610s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001308s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001754s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:08+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001565s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001632s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002096s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:09+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001599s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001237s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001690s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:09+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001631s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000335s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001200s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001653s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-31T00:02:10+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001585s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000316s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001303s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001737s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:11+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001650s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002361s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002805s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:11+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001537s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000347s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001288s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001758s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:12+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001542s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000334s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001248s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001706s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:12+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002204s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000423s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001712s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002286s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:14+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.001592s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000330s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001198s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001646s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:14+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001549s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000315s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001215s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001647s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:15+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001553s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000313s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001213s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001643s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:15+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.001782s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001249s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001694s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:15+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001522s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000310s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001235s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001669s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:16+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001563s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000350s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001211s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001685s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:16+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001604s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001270s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001706s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:18+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001599s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000346s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001232s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001697s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:18+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001540s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001347s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001797s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:18+08:00 ] ************** /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001584s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000314s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001216s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001649s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:18+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001593s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000358s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001259s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001740s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:18+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002813s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001291s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001727s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:20+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001747s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000369s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001429s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001957s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:21+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001694s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000336s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001218s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001676s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:21+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001698s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001212s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001658s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:21+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001602s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000347s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001224s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001694s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:22+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001513s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001244s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001701s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:24+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001527s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000327s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001223s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001664s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:24+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001546s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000315s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001224s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001657s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:24+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001530s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000337s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001217s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001671s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:25+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.002094s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000378s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001358s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001861s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:26+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001543s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001194s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001658s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:26+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001580s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.008964s ]
INFO: [ app_begin ] --END-- [ RunTime:0.009421s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:26+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.011928s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000443s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001329s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001894s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:26+08:00 ] ************** /Ajaxtrade/obtain_usdz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001643s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001255s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001700s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_market` [ RunTime:0.0005s ]
SQL: SELECT `new_price`,`min_price`,`max_price`,`faxingjia`,`volume` FROM `tw_market` WHERE `name` = 'usdz_usdt' LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:27+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001548s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001204s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001646s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:28+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000000s ]
INFO: [ app_init ] --END-- [ RunTime:0.001654s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001222s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001663s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:28+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002043s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001429s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001896s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:30+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001552s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000335s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001226s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001681s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:30+08:00 ] ************** /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001554s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001243s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001679s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:30+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001644s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000330s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001276s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001739s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:31+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001542s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001220s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001676s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:31+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001589s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001251s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001691s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:31+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001996s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000348s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001324s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001798s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:32+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001763s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000316s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001240s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001673s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:32+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001622s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001195s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001645s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:33+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001599s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000360s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001236s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001717s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:33+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.003049s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000371s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001365s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001873s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:34+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001899s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001228s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001669s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:36+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001630s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000356s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001232s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001758s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:36+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001619s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000339s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001264s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001724s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:36+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000027s ]
INFO: [ app_init ] --END-- [ RunTime:0.002487s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000474s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001302s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001924s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:36+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002486s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000517s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001247s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001910s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:37+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001535s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001180s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001618s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:38+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001612s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000363s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001217s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001726s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:39+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000016s ]
INFO: [ app_init ] --END-- [ RunTime:0.001626s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000348s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001257s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001724s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:39+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001830s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000329s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001266s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001712s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:39+08:00 ] ************** /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001558s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000331s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001267s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001718s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:40+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001615s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000315s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001243s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001674s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:41+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001761s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000314s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001198s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001632s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:42+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001550s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001207s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001645s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:42+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001517s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000308s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001235s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001661s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:42+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001650s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001293s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001734s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:44+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001642s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000327s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001205s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001652s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:44+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001512s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000371s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001285s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001773s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:45+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.001661s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000348s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001467s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001941s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:46+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001595s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000329s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001257s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001722s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:46+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001842s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000363s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001458s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001961s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:46+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001761s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000455s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002521s ]
INFO: [ app_begin ] --END-- [ RunTime:0.003188s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:47+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001553s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001234s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001674s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:48+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001552s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000327s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001262s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001710s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:48+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001653s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000340s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001302s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001765s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:48+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001543s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001228s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001672s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:48+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002787s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000493s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001679s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002336s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:50+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001566s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001256s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001706s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:51+08:00 ] ************** /Ajaxtrade/obtain_usdz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001571s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001260s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001702s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_market` [ RunTime:0.0005s ]
SQL: SELECT `new_price`,`min_price`,`max_price`,`faxingjia`,`volume` FROM `tw_market` WHERE `name` = 'usdz_usdt' LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:51+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001600s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001267s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001713s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:51+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001533s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001219s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001674s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:51+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.005680s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001233s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001678s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:51+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001586s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000398s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001258s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001774s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:52+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001541s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000314s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001186s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001620s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:53+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002874s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000327s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001207s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001652s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:53+08:00 ] ************** /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001577s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000336s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001287s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001744s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:54+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001539s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001297s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001747s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:54+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001599s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000365s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001353s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001857s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:56+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001620s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000327s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001323s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001770s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:56+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002254s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001277s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001739s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:57+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001685s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001218s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001674s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:57+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001562s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001193s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001640s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:58+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.001585s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000329s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001198s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001647s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:02:59+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000039s ]
INFO: [ app_init ] --END-- [ RunTime:0.001969s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000502s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001584s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002265s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:00+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000015s ]
INFO: [ app_init ] --END-- [ RunTime:0.001616s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000330s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001224s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001674s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0006s ]

[ 2025-07-31T00:03:00+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001641s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000483s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001232s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001836s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0004s ]

[ 2025-07-31T00:03:00+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001590s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000329s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001269s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001718s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-31T00:03:00+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000006s ]
INFO: [ app_init ] --END-- [ RunTime:0.003009s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001273s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001726s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:00+08:00 ] ************** /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001599s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000339s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001346s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001821s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:01+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001574s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001279s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001722s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:03+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.001704s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000334s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001230s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001749s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:03+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001597s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000338s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001365s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001826s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0006s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'rlYAb' LIMIT 1   [ RunTime:0.0003s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0006s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','2abfb657331f2f30809e272cba829056','5000.00000000','rlYAb','163','0','0','2','',',163','**************','非洲','1753891383','1','1')

[ 2025-07-31T00:03:04+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001633s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000343s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001236s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001705s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:04+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001666s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000372s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001380s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001878s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:05+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001527s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000316s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001195s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001628s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:06+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001559s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001306s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001743s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:07+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001781s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000516s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001662s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002340s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:07+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001583s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001229s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001676s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:07+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001569s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000452s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001422s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001993s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:07+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001581s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001211s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001647s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:07+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.002953s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001220s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001669s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:08+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001592s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000404s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001272s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001799s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:09+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001511s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001183s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001617s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:09+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001991s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000447s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001210s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001777s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:09+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001543s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000327s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001213s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001659s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:10+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001628s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000333s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002257s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002760s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:10+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001729s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000341s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001497s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001993s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:10+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.002593s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000562s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002168s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002906s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:11+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001624s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001292s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001737s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:12+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001645s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001202s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001638s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:12+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001693s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001229s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001684s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:14+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001578s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000337s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001261s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001717s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:14+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.002603s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000490s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001822s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002464s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:15+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001606s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000330s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001266s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001717s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:16+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001929s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000438s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002334s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002917s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:16+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001818s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000355s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001407s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001892s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:16+08:00 ] ************** /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001565s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000344s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001219s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001682s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:16+08:00 ] ************** /Ajaxtrade/obtain_usdz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001566s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000359s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001340s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001821s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_market` [ RunTime:0.0007s ]
SQL: SELECT `new_price`,`min_price`,`max_price`,`faxingjia`,`volume` FROM `tw_market` WHERE `name` = 'usdz_usdt' LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-31T00:03:18+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001602s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001286s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001734s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:18+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001633s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000338s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001375s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001870s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:18+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001605s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000347s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001248s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001713s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-31T00:03:20+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001585s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001218s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001697s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:21+08:00 ] ************** /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001573s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000336s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001281s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001737s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:21+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001653s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001229s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001679s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:21+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001601s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001206s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001653s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0004s ]

[ 2025-07-31T00:03:21+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.001725s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001246s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001693s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:21+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001545s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001261s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001701s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:22+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.007173s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000455s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001287s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001869s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:22+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001732s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000443s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001288s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001873s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:22+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001606s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000360s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001288s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001790s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:23+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001592s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000329s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001204s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001650s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:24+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001588s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001208s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001650s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:24+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001561s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001211s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001655s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:25+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001554s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000335s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001199s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001659s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:26+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001570s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000336s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001263s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001720s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:26+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001643s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000334s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001252s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001706s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:27+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001530s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001253s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001693s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:27+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001534s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001203s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001643s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:28+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001529s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000315s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001220s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001660s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:28+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001589s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000327s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001386s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001832s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:30+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001554s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001294s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001727s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:30+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001570s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000313s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001221s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001650s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:31+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001548s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001268s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001706s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:31+08:00 ] ************** /Home/Autoexe/hycarryout
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001513s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000315s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001177s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001602s ]
SQL: SHOW COLUMNS FROM `tw_hyorder` [ RunTime:0.0005s ]
SQL: SELECT * FROM `tw_hyorder` WHERE `status` = 1 AND `intselltime` <= 1753891411  [ RunTime:0.0004s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0003s ]
SQL: SELECT `hy_ksid`,`hy_ylid`,`hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000351s ]
INFO: [ app_end ] --END-- [ RunTime:0.000410s ]

[ 2025-07-31T00:03:32+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001566s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001224s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001663s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:32+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001632s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001567s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002016s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:33+08:00 ] ************** /Home/Autoexe/hycarryout_ty
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001569s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000330s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001216s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001655s ]
SQL: SHOW COLUMNS FROM `tw_tyhyorder` [ RunTime:0.0007s ]
SQL: SELECT * FROM `tw_tyhyorder` WHERE `status` = 1 AND `intselltime` <= 1753891413  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0004s ]
SQL: SELECT `hy_ksid`,`hy_ylid`,`hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000377s ]
INFO: [ app_end ] --END-- [ RunTime:0.000441s ]

[ 2025-07-31T00:03:33+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001665s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000409s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001580s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002125s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:34+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001583s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001207s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001650s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:34+08:00 ] ************** /Home/Autoexe/setwl
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001515s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000327s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001213s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001651s ]
SQL: SHOW COLUMNS FROM `tw_hyorder` [ RunTime:0.0006s ]
SQL: SELECT COUNT(*) AS tp_count FROM `tw_hyorder` WHERE `status` = 1 AND `kongyk` = 0 LIMIT 1   [ RunTime:0.0005s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0004s ]
SQL: SELECT `hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000403s ]
INFO: [ app_end ] --END-- [ RunTime:0.000468s ]

[ 2025-07-31T00:03:35+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001556s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000310s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001238s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001669s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:36+08:00 ] ************** /Home/Autoexe/setwl_ty
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.001797s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000353s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001253s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001721s ]
SQL: SHOW COLUMNS FROM `tw_tyhyorder` [ RunTime:0.0006s ]
SQL: SELECT COUNT(*) AS tp_count FROM `tw_tyhyorder` WHERE `status` = 1 AND `kongyk` = 0 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_hysetting` [ RunTime:0.0004s ]
SQL: SELECT `hy_fkgl` FROM `tw_hysetting` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000536s ]
INFO: [ app_end ] --END-- [ RunTime:0.000646s ]

[ 2025-07-31T00:03:36+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001568s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001202s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001639s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:36+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001657s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001349s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001798s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:36+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.001646s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000345s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001357s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001839s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:36+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.011831s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000348s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001356s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001862s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:36+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001776s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000541s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001830s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002531s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:37+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000021s ]
INFO: [ app_init ] --END-- [ RunTime:0.001643s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000382s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001337s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001839s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:37+08:00 ] ************** /Home/Autoexe/autoxjtade
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001529s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000314s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001197s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001627s ]
SQL: SHOW COLUMNS FROM `tw_bborder` [ RunTime:0.0006s ]
SQL: SELECT * FROM `tw_bborder` WHERE `ordertype` = 1 AND `status` = 1  [ RunTime:0.0002s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000365s ]
INFO: [ app_end ] --END-- [ RunTime:0.000428s ]

[ 2025-07-31T00:03:38+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.002329s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001322s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001764s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:39+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001602s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001543s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001988s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:39+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001564s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001280s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001736s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:39+08:00 ] ************** /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001572s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001336s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001771s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:40+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001584s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000316s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001175s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001607s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:41+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001638s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001196s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001631s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:41+08:00 ] ************** /Ajaxtrade/obtain_usdz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001577s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001829s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002697s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_market` [ RunTime:0.0004s ]
SQL: SELECT `new_price`,`min_price`,`max_price`,`faxingjia`,`volume` FROM `tw_market` WHERE `name` = 'usdz_usdt' LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:42+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001586s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001221s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001673s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:42+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001581s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001352s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001799s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:42+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001711s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000329s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001213s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001660s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:42+08:00 ] ************** /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001679s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000364s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001266s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001761s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:43+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001527s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001227s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001670s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:44+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001519s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001210s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001654s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:44+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001568s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001192s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001626s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0006s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:45+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001620s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001195s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001637s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:45+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.002436s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000452s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001691s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002324s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:46+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001529s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001218s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001657s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:46+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001617s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001265s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001710s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:48+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001541s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001193s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001640s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:03:48+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001744s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000363s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001321s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001804s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

