[ 2025-07-31T00:22:36+08:00 ] ************** /Public/Dela/template/dela-template.css.map
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001525s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000349s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001248s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001741s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.

[ 2025-07-31T00:22:37+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001645s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000337s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001249s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001755s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:37+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001624s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001243s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001685s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:37+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001615s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000334s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001248s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001703s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:37+08:00 ] ************** /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001589s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000330s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001218s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001664s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:37+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001533s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000339s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001191s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001650s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:37+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.003813s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000324s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001222s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001674s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:37+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001563s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000508s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001293s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001925s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:39+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001792s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000350s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001444s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001913s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:39+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001672s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001223s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001666s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:40+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001600s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001208s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001645s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:40+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001597s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000330s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001281s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001728s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:41+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001784s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000355s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001285s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001761s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:41+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.002261s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000547s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002217s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002954s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:42+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001571s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000334s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001446s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001899s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:42+08:00 ] ************** /Login/upregister?account=rista31%40511.la&ecode=&lpwd=XGVFDNEWQ&invit=XGVFDNEWQ&type=2
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001537s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000356s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001299s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001791s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'XGVFDNEWQ' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'kEjFh' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','6cbb26630713780a4b4b7f1327a17aa5','5000.00000000','kEjFh','165','0','0','2','',',165','**************','非洲','1753892562','1','1')

[ 2025-07-31T00:22:43+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001499s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000315s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001203s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001636s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:43+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001522s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000333s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001283s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001734s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:44+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001533s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000438s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001737s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002327s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:45+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001666s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000318s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001240s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001678s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:45+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001531s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001255s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001701s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:45+08:00 ] ************** /Ajaxtrade/getcoinprice
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001656s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001215s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001655s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:45+08:00 ] ************** /Ajaxtrade/gettradbuyten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.003178s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000329s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001303s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001756s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:45+08:00 ] ************** /Ajaxtrade/gettradsellten
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001608s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000349s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001311s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001801s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0004s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:46+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001801s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000328s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001302s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001753s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:47+08:00 ] ************** /Ajaxtrade/getallcoin
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001517s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000390s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001359s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001934s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0003s ]
SQL: SELECT `coinname`,`id` FROM `tw_ctmarket` WHERE `status` = 1 AND `coinname` <> 'usdz'  [ RunTime:0.0002s ]
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.
NOTIC: [8] Undefined index: data /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 931 行.
NOTIC: [2] Division by zero /www/wwwroot/www.3728.ltd/Application/Home/Controller/AjaxtradeController.class.php 第 935 行.

[ 2025-07-31T00:22:47+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001777s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001232s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001674s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:47+08:00 ] ************** /Login/upregister?account=rista31%40511.la&ecode=&lpwd=XGVFDNEWQ&invit=XGVFDNEWQ&type=2
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001735s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000361s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001253s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001739s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0001s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'XGVFDNEWQ' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'vckBq' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','6cbb26630713780a4b4b7f1327a17aa5','5000.00000000','vckBq','165','0','0','2','',',165','**************','非洲','1753892567','1','1')

[ 2025-07-31T00:22:47+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001780s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001250s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001708s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:48+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001626s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000342s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001233s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001699s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:49+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000005s ]
INFO: [ app_init ] --END-- [ RunTime:0.001539s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000316s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001198s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001631s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:49+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001558s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000320s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001253s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001693s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:49+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.001857s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000335s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001251s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001716s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:50+08:00 ] ************** /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001541s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000339s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001179s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001634s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:51+08:00 ] ************** /Login/upregister?account=rista31%40511.la&ecode=&lpwd=XGVFDNEWQ&invit=XGVFDNEWQ&type=2
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001618s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001314s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001764s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'XGVFDNEWQ' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'RJPiH' LIMIT 1   [ RunTime:0.0003s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0004s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','6cbb26630713780a4b4b7f1327a17aa5','5000.00000000','RJPiH','165','0','0','2','',',165','**************','非洲','1753892571','1','1')

[ 2025-07-31T00:22:51+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001532s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000317s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001264s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001705s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:51+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001533s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000323s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001173s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001617s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:51+08:00 ] ************** /Ajaxtrade/obtain_usdz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001549s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000366s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001215s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001699s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_market` [ RunTime:0.0004s ]
SQL: SELECT `new_price`,`min_price`,`max_price`,`faxingjia`,`volume` FROM `tw_market` WHERE `name` = 'usdz_usdt' LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:52+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001548s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000313s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001231s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001669s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:52+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001591s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000367s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001210s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001722s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:52+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001666s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000354s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001320s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001796s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:52+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001552s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000321s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001210s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001651s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:53+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001562s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000325s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001254s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001723s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:54+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001583s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000309s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001185s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001609s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:54+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000007s ]
INFO: [ app_init ] --END-- [ RunTime:0.001594s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000309s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001185s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001611s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:55+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002447s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001207s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001645s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:56+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001620s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000330s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001275s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001724s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:56+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001650s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000409s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001237s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001766s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:57+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001588s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000327s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001206s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001648s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:57+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001526s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000315s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001279s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001719s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:57+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001586s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000319s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001231s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001670s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:57+08:00 ] ************** /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001555s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001193s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001643s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:58+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001996s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000335s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001251s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001711s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-31T00:22:58+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002511s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000335s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001389s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001845s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

