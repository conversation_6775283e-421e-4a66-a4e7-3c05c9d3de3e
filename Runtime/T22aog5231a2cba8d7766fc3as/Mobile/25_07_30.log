[ 2025-07-30T00:24:50+08:00 ] 78.153.140.203 /backend/.env
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002014s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000506s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001477s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002152s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.

[ 2025-07-30T00:25:06+08:00 ] 78.153.140.203 /config/.env
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002050s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000372s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001584s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002143s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.

[ 2025-07-30T00:25:07+08:00 ] 78.153.140.203 /debug/default/view?panel=config
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002018s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000448s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001653s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002280s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.

[ 2025-07-30T01:01:24+08:00 ] 35.94.177.95 /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002338s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000365s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001532s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002062s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-30T01:02:14+08:00 ] 66.249.74.165 /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002267s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000396s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001403s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001956s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0012s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-30T01:02:16+08:00 ] 66.249.76.65 /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002442s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000374s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001608s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002142s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0013s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0006s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0004s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0006s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0003s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000132s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000225s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.013797s ]
INFO: [ view_parse ] --END-- [ RunTime:0.013905s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000221s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000293s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000441s ]
INFO: [ app_end ] --END-- [ RunTime:0.000612s ]

[ 2025-07-30T01:24:44+08:00 ] 66.249.74.165 /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002233s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000372s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001508s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002043s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-30T01:24:44+08:00 ] 66.249.74.164 /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002085s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000349s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001620s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002146s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-30T01:24:45+08:00 ] 66.249.74.164 /Ajaxtrade/obtain_usdz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002303s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000478s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001388s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002016s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_market` [ RunTime:0.0010s ]
SQL: SELECT `new_price`,`min_price`,`max_price`,`faxingjia`,`volume` FROM `tw_market` WHERE `name` = 'usdz_usdt' LIMIT 1   [ RunTime:0.0004s ]
NOTIC: [8] Undefined variable: cname /www/wwwroot/lumao.cc/Application/Mobile/Controller/AjaxtradeController.class.php 第 696 行.

[ 2025-07-30T01:24:45+08:00 ] 66.249.74.165 /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000031s ]
INFO: [ app_init ] --END-- [ RunTime:0.002244s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000521s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001590s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002301s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-30T01:24:46+08:00 ] 66.249.74.164 /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002088s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000377s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001325s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001864s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0013s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-30T01:24:47+08:00 ] 66.249.74.164 /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000015s ]
INFO: [ app_init ] --END-- [ RunTime:0.002233s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000398s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001658s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002223s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-30T01:24:47+08:00 ] 66.249.74.164 /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002309s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000401s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001533s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002089s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-30T01:24:48+08:00 ] 66.249.76.64 /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001964s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000381s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001420s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001972s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-30T01:24:49+08:00 ] 66.249.76.78 /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002501s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000453s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001811s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002450s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-30T01:24:49+08:00 ] 66.249.76.78 /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002048s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000430s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001473s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002057s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-30T01:24:50+08:00 ] 66.249.74.165 /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002215s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000392s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001515s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002058s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-30T01:24:50+08:00 ] 66.249.74.164 /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.002523s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000411s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001653s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002243s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0012s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-30T01:24:52+08:00 ] 66.249.76.78 /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002037s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000348s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001332s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001813s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0014s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-30T01:24:53+08:00 ] 66.249.76.78 /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000015s ]
INFO: [ app_init ] --END-- [ RunTime:0.002337s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000388s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001546s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002127s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-30T01:24:54+08:00 ] 66.249.76.78 /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000019s ]
INFO: [ app_init ] --END-- [ RunTime:0.002912s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000535s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001925s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002648s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-30T02:09:07+08:00 ] 121.229.185.160 /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002479s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000341s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001368s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001847s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-30T02:09:07+08:00 ] 121.229.185.160 /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002118s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000461s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001647s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002297s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0012s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0006s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0010s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0003s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0003s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000139s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000243s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.016254s ]
INFO: [ view_parse ] --END-- [ RunTime:0.016358s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000220s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000287s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000499s ]
INFO: [ app_end ] --END-- [ RunTime:0.000597s ]

[ 2025-07-30T02:50:00+08:00 ] 43.159.152.187 /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002278s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000395s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001869s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002423s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-30T02:50:01+08:00 ] 43.159.152.187 /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002595s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000467s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002360s ]
INFO: [ app_begin ] --END-- [ RunTime:0.003056s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0006s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0005s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0003s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000160s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000286s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.016092s ]
INFO: [ view_parse ] --END-- [ RunTime:0.016205s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000221s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000292s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000512s ]
INFO: [ app_end ] --END-- [ RunTime:0.000621s ]

[ 2025-07-30T06:02:28+08:00 ] 78.153.140.151 /.env-devtools
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002312s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000402s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002133s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002722s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-30T06:02:29+08:00 ] 78.153.140.151 /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002450s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000464s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001685s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002325s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0005s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0006s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0003s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0003s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000123s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000239s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.013991s ]
INFO: [ view_parse ] --END-- [ RunTime:0.014068s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000189s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000255s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000400s ]
INFO: [ app_end ] --END-- [ RunTime:0.000479s ]

[ 2025-07-30T06:02:37+08:00 ] 78.153.140.151 /.env-fixtures
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001987s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000458s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001746s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002421s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0011s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-30T06:02:38+08:00 ] 78.153.140.151 /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002140s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000421s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001502s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002096s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0004s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0005s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0003s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000159s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000297s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.015093s ]
INFO: [ view_parse ] --END-- [ RunTime:0.015163s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000215s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000279s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000487s ]
INFO: [ app_end ] --END-- [ RunTime:0.000565s ]

[ 2025-07-30T06:02:42+08:00 ] 78.153.140.151 /.env-heapdump
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002009s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000390s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001439s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002046s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0013s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-30T06:02:43+08:00 ] 78.153.140.151 /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.001962s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000426s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001549s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002143s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0011s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0007s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0005s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0003s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000187s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000360s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.016258s ]
INFO: [ view_parse ] --END-- [ RunTime:0.016370s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000287s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000380s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000546s ]
INFO: [ app_end ] --END-- [ RunTime:0.000661s ]

[ 2025-07-30T06:03:01+08:00 ] 78.153.140.151 /.env-mail.log
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002332s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000568s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001519s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002304s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.

[ 2025-07-30T06:03:06+08:00 ] 78.153.140.151 /.env-node.log
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002119s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000366s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001406s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001916s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.

[ 2025-07-30T07:55:56+08:00 ] 147.124.219.135 /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002390s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000358s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001476s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002011s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0020s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0006s ]

[ 2025-07-30T08:56:55+08:00 ] 45.148.10.80 /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002126s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000359s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001406s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001912s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-30T10:14:15+08:00 ] 78.153.140.203 /api/.env
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002062s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000392s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001368s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001911s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.

[ 2025-07-30T10:15:11+08:00 ] 43.155.195.141 /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002263s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000366s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001445s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002003s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-30T10:15:12+08:00 ] 43.155.195.141 /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.001932s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000379s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001362s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001883s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0006s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0009s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0003s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000132s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000313s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.016841s ]
INFO: [ view_parse ] --END-- [ RunTime:0.016941s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000209s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000275s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000510s ]
INFO: [ app_end ] --END-- [ RunTime:0.000595s ]

[ 2025-07-30T10:20:49+08:00 ] 141.148.153.213 /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000015s ]
INFO: [ app_init ] --END-- [ RunTime:0.002425s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000393s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001470s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002046s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-30T10:20:50+08:00 ] 141.148.153.213 /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000019s ]
INFO: [ app_init ] --END-- [ RunTime:0.003028s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000561s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002331s ]
INFO: [ app_begin ] --END-- [ RunTime:0.003118s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0005s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0005s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0003s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0003s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000161s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000380s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.018988s ]
INFO: [ view_parse ] --END-- [ RunTime:0.019069s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000225s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000304s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000455s ]
INFO: [ app_end ] --END-- [ RunTime:0.000536s ]

[ 2025-07-30T10:35:58+08:00 ] 141.148.153.213 /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000016s ]
INFO: [ app_init ] --END-- [ RunTime:0.003154s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000541s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002232s ]
INFO: [ app_begin ] --END-- [ RunTime:0.003018s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0004s ]

[ 2025-07-30T10:35:58+08:00 ] 141.148.153.213 /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002185s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000384s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001590s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002156s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0006s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0006s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0003s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000136s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000253s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.015703s ]
INFO: [ view_parse ] --END-- [ RunTime:0.015808s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000212s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000300s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000894s ]
INFO: [ app_end ] --END-- [ RunTime:0.001006s ]

[ 2025-07-30T10:47:08+08:00 ] 141.148.153.213 /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002496s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000450s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001719s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002324s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-30T10:47:08+08:00 ] 141.148.153.213 /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.002012s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000341s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001302s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001774s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0006s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0005s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000114s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000209s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.014099s ]
INFO: [ view_parse ] --END-- [ RunTime:0.014170s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000185s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000250s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000504s ]
INFO: [ app_end ] --END-- [ RunTime:0.000585s ]

[ 2025-07-30T12:11:32+08:00 ] 165.154.110.5 /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002676s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000482s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001650s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002322s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0011s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-30T12:11:32+08:00 ] 165.154.110.5 /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002270s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000360s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001639s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002170s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0014s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0008s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0007s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0005s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0003s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000182s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000402s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.019935s ]
INFO: [ view_parse ] --END-- [ RunTime:0.020069s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000304s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000410s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000561s ]
INFO: [ app_end ] --END-- [ RunTime:0.000695s ]

[ 2025-07-30T12:16:44+08:00 ] 165.154.110.5 /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000015s ]
INFO: [ app_init ] --END-- [ RunTime:0.003453s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000528s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002321s ]
INFO: [ app_begin ] --END-- [ RunTime:0.003069s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0011s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-30T12:16:44+08:00 ] 165.154.110.5 /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.002697s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000421s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001601s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002213s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0006s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0006s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0003s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0003s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000152s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000361s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.017059s ]
INFO: [ view_parse ] --END-- [ RunTime:0.017156s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000259s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000344s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000477s ]
INFO: [ app_end ] --END-- [ RunTime:0.000562s ]

[ 2025-07-30T12:26:18+08:00 ] 165.154.110.5 /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000017s ]
INFO: [ app_init ] --END-- [ RunTime:0.002579s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000336s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001442s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001925s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-30T12:26:18+08:00 ] 165.154.110.5 /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002375s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000421s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001462s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002030s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0017s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0004s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0007s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0007s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0003s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000122s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000235s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.013854s ]
INFO: [ view_parse ] --END-- [ RunTime:0.013957s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000187s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000252s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000454s ]
INFO: [ app_end ] --END-- [ RunTime:0.000534s ]

[ 2025-07-30T12:28:15+08:00 ] 165.154.110.5 /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000018s ]
INFO: [ app_init ] --END-- [ RunTime:0.002756s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000462s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001494s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002137s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-30T12:28:15+08:00 ] 165.154.110.5 /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000016s ]
INFO: [ app_init ] --END-- [ RunTime:0.002404s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000468s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001602s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002214s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0005s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0005s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0003s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000154s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000319s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.017331s ]
INFO: [ view_parse ] --END-- [ RunTime:0.017404s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000236s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000306s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000532s ]
INFO: [ app_end ] --END-- [ RunTime:0.000617s ]

[ 2025-07-30T14:19:43+08:00 ] 123.6.49.47 /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000031s ]
INFO: [ app_init ] --END-- [ RunTime:0.002313s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000438s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001677s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002298s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-30T14:19:43+08:00 ] 123.6.49.47 /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002116s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000368s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001372s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001877s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0006s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0007s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0003s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0003s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000148s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000328s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.016968s ]
INFO: [ view_parse ] --END-- [ RunTime:0.017044s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000280s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000382s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000522s ]
INFO: [ app_end ] --END-- [ RunTime:0.000630s ]

[ 2025-07-30T14:27:13+08:00 ] 182.44.9.147 /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002618s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000479s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002201s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002884s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-30T14:27:20+08:00 ] 182.44.9.147 /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002153s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000387s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001378s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001931s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0011s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0005s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0005s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0003s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000128s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000249s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.016204s ]
INFO: [ view_parse ] --END-- [ RunTime:0.016311s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000215s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000304s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000436s ]
INFO: [ app_end ] --END-- [ RunTime:0.000520s ]

[ 2025-07-30T14:45:46+08:00 ] 124.223.193.80 /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002375s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000398s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001497s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002058s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-30T14:46:03+08:00 ] 124.223.193.80 /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002160s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000366s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001408s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001925s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-30T14:46:18+08:00 ] 124.223.193.80 /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000016s ]
INFO: [ app_init ] --END-- [ RunTime:0.002336s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000352s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001496s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001997s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0005s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0004s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000167s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000297s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.017008s ]
INFO: [ view_parse ] --END-- [ RunTime:0.017096s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000209s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000306s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000612s ]
INFO: [ app_end ] --END-- [ RunTime:0.000741s ]

[ 2025-07-30T15:45:48+08:00 ] 43.157.38.228 /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002399s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000386s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001644s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002223s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-30T15:45:49+08:00 ] 43.157.38.228 /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002140s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000358s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001461s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001998s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0006s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0006s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000175s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000318s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.015207s ]
INFO: [ view_parse ] --END-- [ RunTime:0.015284s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000243s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000344s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000446s ]
INFO: [ app_end ] --END-- [ RunTime:0.000527s ]

[ 2025-07-30T16:26:04+08:00 ] 183.134.59.131 /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002365s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000398s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001476s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002044s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-30T16:26:05+08:00 ] 183.134.59.131 /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002142s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000374s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001449s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001972s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0006s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0005s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0003s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000106s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000187s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.014135s ]
INFO: [ view_parse ] --END-- [ RunTime:0.014214s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000225s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000289s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000407s ]
INFO: [ app_end ] --END-- [ RunTime:0.000487s ]

[ 2025-07-30T16:32:42+08:00 ] 183.134.59.131 /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002457s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000350s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001464s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001996s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0012s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-30T23:41:38+08:00 ] 209.97.180.8 /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001926s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000380s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001366s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001875s ]

[ 2025-07-30T23:41:41+08:00 ] 207.154.212.47 /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002143s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000358s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001461s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001947s ]

[ 2025-07-30T23:46:21+08:00 ] 154.201.86.183 /Trade/index?type=buy&symbol=BTC
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001803s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000335s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001230s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001705s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-30T23:46:21+08:00 ] 154.201.86.183 /Login/index
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001986s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000334s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001215s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001672s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0005s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000045s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000131s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/www.3728.ltd/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.005559s ]
INFO: [ view_parse ] --END-- [ RunTime:0.005625s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000184s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000241s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000377s ]
INFO: [ app_end ] --END-- [ RunTime:0.000451s ]

