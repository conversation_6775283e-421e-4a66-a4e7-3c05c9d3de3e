[ 2025-07-31T00:01:07+08:00 ] ************** /Login/upregister
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001554s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000322s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001256s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001698s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0006s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `tymoney` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `id`,`username`,`invit_1`,`invit_2`,`path` FROM `tw_user` WHERE `invit` = 'CVWENMHBY' LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_user` WHERE `invit` = 'Yxjgz' LIMIT 1   [ RunTime:0.0002s ]
SQL: set autocommit=0 [ RunTime:0.0001s ]
SQL: lock tables tw_user write , tw_user_coin write  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0006s ]
ERR: 1054:Unknown column 'type' in 'field list'
 [ SQL语句 ] : INSERT INTO `tw_user` (`username`,`password`,`money`,`invit`,`invit_1`,`invit_2`,`invit_3`,`type`,`area_code`,`path`,`addip`,`addr`,`addtime`,`status`,`txstate`) VALUES ('<EMAIL>','2abfb657331f2f30809e272cba829056','5000.00000000','Yxjgz','163','0','0','2','',',163','**************','非洲','1753891267','1','1')

