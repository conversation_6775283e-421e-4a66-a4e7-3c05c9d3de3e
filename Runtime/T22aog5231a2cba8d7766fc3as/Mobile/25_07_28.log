[ 2025-07-28T18:07:33+08:00 ] ************** /Index/uoption
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002319s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000530s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001826s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002561s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0006s ]
SQL: SELECT `id`,`username`,`invit`,`rzstatus` FROM `tw_user` WHERE `id` = 162 LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `weblogo` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_notice` [ RunTime:0.0005s ]
SQL: SELECT COUNT(*) AS tp_count FROM `tw_notice` WHERE `uid` = 162 AND `status` = 1 LIMIT 1   [ RunTime:0.0008s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000069s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000158s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.009397s ]
INFO: [ view_parse ] --END-- [ RunTime:0.009488s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000180s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000251s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000485s ]
INFO: [ app_end ] --END-- [ RunTime:0.000624s ]

[ 2025-07-28T18:07:35+08:00 ] ************** /User/authrz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000015s ]
INFO: [ app_init ] --END-- [ RunTime:0.002386s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000420s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001589s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002160s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0007s ]
SQL: SELECT * FROM `tw_user` WHERE `id` = 162 LIMIT 1   [ RunTime:0.0003s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000092s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000189s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8] Undefined index: area /www/wwwroot/lumao.cc/Runtime/Cache/Mobile/3d0f2a07f30525c9816895171db7aa7f.php 第 133 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8] Undefined index: real_name /www/wwwroot/lumao.cc/Runtime/Cache/Mobile/3d0f2a07f30525c9816895171db7aa7f.php 第 140 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8] Undefined index: rztype /www/wwwroot/lumao.cc/Runtime/Cache/Mobile/3d0f2a07f30525c9816895171db7aa7f.php 第 155 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8] Undefined index: rztype /www/wwwroot/lumao.cc/Runtime/Cache/Mobile/3d0f2a07f30525c9816895171db7aa7f.php 第 156 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8] Undefined index: rztype /www/wwwroot/lumao.cc/Runtime/Cache/Mobile/3d0f2a07f30525c9816895171db7aa7f.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8] Undefined index: rztype /www/wwwroot/lumao.cc/Runtime/Cache/Mobile/3d0f2a07f30525c9816895171db7aa7f.php 第 158 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.010559s ]
INFO: [ view_parse ] --END-- [ RunTime:0.010638s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000193s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000261s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000466s ]
INFO: [ app_end ] --END-- [ RunTime:0.000564s ]

[ 2025-07-28T18:07:51+08:00 ] ************** /Ajaxtrade/getcoin_data
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001879s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000368s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001302s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001804s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:07:51+08:00 ] ************** /Contract/gethyorder
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002203s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000391s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001591s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002148s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0012s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_hyorder` [ RunTime:0.0008s ]
SQL: SELECT `id`,`num`,`coinname`,`status`,`buytime`,`selltime`,`buyprice`,`time`,`hyzd` FROM `tw_hyorder` WHERE `uid` = 162 ORDER BY id desc  [ RunTime:0.0008s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.

[ 2025-07-28T18:07:51+08:00 ] ************** /Contract/get_hyorder_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002453s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000360s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001435s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001974s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_hyorder` [ RunTime:0.0008s ]
SQL: SELECT `id`,`buytime`,`time`,`status`,`coinname`,`selltime`,`is_win`,`ploss` FROM `tw_hyorder` WHERE `id` = 691 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:07:52+08:00 ] ************** /Ajaxtrade/getcoin_data
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000015s ]
INFO: [ app_init ] --END-- [ RunTime:0.002232s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000498s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001859s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002574s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:07:52+08:00 ] ************** /Contract/gethyorder
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002160s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000459s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001531s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002142s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0014s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_hyorder` [ RunTime:0.0006s ]
SQL: SELECT `id`,`num`,`coinname`,`status`,`buytime`,`selltime`,`buyprice`,`time`,`hyzd` FROM `tw_hyorder` WHERE `uid` = 162 ORDER BY id desc  [ RunTime:0.0006s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.

[ 2025-07-28T18:07:53+08:00 ] ************** /Contract/get_hyorder_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001999s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000342s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001398s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001872s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0011s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_hyorder` [ RunTime:0.0006s ]
SQL: SELECT `id`,`buytime`,`time`,`status`,`coinname`,`selltime`,`is_win`,`ploss` FROM `tw_hyorder` WHERE `id` = 691 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:08:29+08:00 ] ************** /Index/index
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001899s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000332s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001339s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001801s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:08:30+08:00 ] ************** /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002067s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000397s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001474s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002018s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0006s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0006s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0003s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000139s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000328s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.015721s ]
INFO: [ view_parse ] --END-- [ RunTime:0.015797s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000214s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000278s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000494s ]
INFO: [ app_end ] --END-- [ RunTime:0.000578s ]

[ 2025-07-28T18:08:30+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002220s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000386s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001598s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002129s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:08:30+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002064s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000381s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001503s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002023s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:08:30+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002138s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000443s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001542s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002158s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:08:31+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002330s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000412s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001621s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002200s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:08:31+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.002217s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000436s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001556s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002180s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:08:31+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000018s ]
INFO: [ app_init ] --END-- [ RunTime:0.002989s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000533s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002181s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002895s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:08:31+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000017s ]
INFO: [ app_init ] --END-- [ RunTime:0.003017s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000546s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002295s ]
INFO: [ app_begin ] --END-- [ RunTime:0.003017s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0012s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:08:31+08:00 ] ************** /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002882s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000473s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002168s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002809s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:08:32+08:00 ] ************** /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002897s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000493s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001937s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002594s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0004s ]

[ 2025-07-28T18:08:32+08:00 ] ************** /Ajaxtrade/obtain_usdz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.003190s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000555s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002294s ]
INFO: [ app_begin ] --END-- [ RunTime:0.003053s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0013s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_market` [ RunTime:0.0010s ]
SQL: SELECT `new_price`,`min_price`,`max_price`,`faxingjia`,`volume` FROM `tw_market` WHERE `name` = 'usdz_usdt' LIMIT 1   [ RunTime:0.0004s ]
NOTIC: [8] Undefined variable: cname /www/wwwroot/lumao.cc/Application/Mobile/Controller/AjaxtradeController.class.php 第 696 行.

[ 2025-07-28T18:08:32+08:00 ] ************** /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002074s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000439s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001569s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002185s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:08:32+08:00 ] ************** /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000032s ]
INFO: [ app_init ] --END-- [ RunTime:0.002247s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000473s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001742s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002388s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:08:33+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002401s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000601s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002347s ]
INFO: [ app_begin ] --END-- [ RunTime:0.003107s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:08:33+08:00 ] ************** /User/online
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002430s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000488s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001496s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002160s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:08:33+08:00 ] ************** /Login/index
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001932s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000347s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001350s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001831s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000046s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000132s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.005996s ]
INFO: [ view_parse ] --END-- [ RunTime:0.006092s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000172s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000246s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000390s ]
INFO: [ app_end ] --END-- [ RunTime:0.000487s ]

[ 2025-07-28T18:09:03+08:00 ] ************** /Login/loginsubmit
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002030s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000341s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001559s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002040s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0007s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '<EMAIL>' LIMIT 1   [ RunTime:0.0004s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0005s ]
SQL: UPDATE `tw_user` SET `logins`=logins+1 WHERE `id` = 162 [ RunTime:0.0037s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
SQL: SHOW COLUMNS FROM `tw_user_log` [ RunTime:0.0005s ]
SQL: INSERT INTO `tw_user_log` (`userid`,`type`,`remark`,`addtime`,`addip`,`addr`,`status`) VALUES ('162','登录','邮箱登录','1753697343','**************','亚太地区','1') [ RunTime:0.0033s ]
SQL: UPDATE `tw_user` SET `lgtime`='2025-07-28' WHERE `id` = 162 [ RunTime:0.0030s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.

[ 2025-07-28T18:09:05+08:00 ] ************** /Index/index
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002042s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000342s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001330s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001805s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:09:06+08:00 ] ************** /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002011s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000342s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001427s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001911s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0005s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_notice` [ RunTime:0.0006s ]
SQL: SELECT COUNT(*) AS tp_count FROM `tw_notice` WHERE `uid` = 162 AND `status` = 1 LIMIT 1   [ RunTime:0.0006s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0006s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0003s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000122s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000227s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.014755s ]
INFO: [ view_parse ] --END-- [ RunTime:0.014835s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000199s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000275s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000405s ]
INFO: [ app_end ] --END-- [ RunTime:0.000485s ]

[ 2025-07-28T18:09:06+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.003025s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000497s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002127s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002842s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:09:06+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000015s ]
INFO: [ app_init ] --END-- [ RunTime:0.002959s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000508s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002206s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002905s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:09:06+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002603s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000409s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001814s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002385s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:09:07+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.003010s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000467s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002233s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002909s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:09:07+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000015s ]
INFO: [ app_init ] --END-- [ RunTime:0.002992s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000498s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002204s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002894s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:09:07+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002450s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000388s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001662s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002200s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:09:07+08:00 ] ************** /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002039s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000394s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001593s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002138s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:09:07+08:00 ] ************** /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000016s ]
INFO: [ app_init ] --END-- [ RunTime:0.002982s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000502s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002166s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002842s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:09:07+08:00 ] ************** /Ajaxtrade/obtain_usdz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002997s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000464s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001702s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002340s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_market` [ RunTime:0.0007s ]
SQL: SELECT `new_price`,`min_price`,`max_price`,`faxingjia`,`volume` FROM `tw_market` WHERE `name` = 'usdz_usdt' LIMIT 1   [ RunTime:0.0003s ]
NOTIC: [8] Undefined variable: cname /www/wwwroot/lumao.cc/Application/Mobile/Controller/AjaxtradeController.class.php 第 696 行.

[ 2025-07-28T18:09:08+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000015s ]
INFO: [ app_init ] --END-- [ RunTime:0.003256s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000397s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001621s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002168s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:09:08+08:00 ] ************** /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002450s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000675s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001563s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002408s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0011s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:09:08+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.002326s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000373s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001819s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002360s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:09:09+08:00 ] ************** /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002530s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000496s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002047s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002729s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:09:09+08:00 ] ************** /Index/uoption
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001922s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000363s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001325s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001822s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0009s ]
SQL: SELECT `id`,`username`,`invit`,`rzstatus` FROM `tw_user` WHERE `id` = 162 LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `weblogo` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_notice` [ RunTime:0.0004s ]
SQL: SELECT COUNT(*) AS tp_count FROM `tw_notice` WHERE `uid` = 162 AND `status` = 1 LIMIT 1   [ RunTime:0.0005s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000086s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000210s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.010926s ]
INFO: [ view_parse ] --END-- [ RunTime:0.011025s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000199s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000281s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000604s ]
INFO: [ app_end ] --END-- [ RunTime:0.000735s ]

[ 2025-07-28T18:09:10+08:00 ] ************** /User/authrz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002585s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000477s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002178s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002836s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0008s ]
SQL: SELECT * FROM `tw_user` WHERE `id` = 162 LIMIT 1   [ RunTime:0.0003s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000065s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000164s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8] Undefined index: area /www/wwwroot/lumao.cc/Runtime/Cache/Mobile/3d0f2a07f30525c9816895171db7aa7f.php 第 133 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8] Undefined index: real_name /www/wwwroot/lumao.cc/Runtime/Cache/Mobile/3d0f2a07f30525c9816895171db7aa7f.php 第 140 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8] Undefined index: rztype /www/wwwroot/lumao.cc/Runtime/Cache/Mobile/3d0f2a07f30525c9816895171db7aa7f.php 第 155 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8] Undefined index: rztype /www/wwwroot/lumao.cc/Runtime/Cache/Mobile/3d0f2a07f30525c9816895171db7aa7f.php 第 156 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8] Undefined index: rztype /www/wwwroot/lumao.cc/Runtime/Cache/Mobile/3d0f2a07f30525c9816895171db7aa7f.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8] Undefined index: rztype /www/wwwroot/lumao.cc/Runtime/Cache/Mobile/3d0f2a07f30525c9816895171db7aa7f.php 第 158 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.009495s ]
INFO: [ view_parse ] --END-- [ RunTime:0.009569s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000248s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000328s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000431s ]
INFO: [ app_end ] --END-- [ RunTime:0.000511s ]

[ 2025-07-28T18:09:55+08:00 ] ************** /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002073s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000345s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001589s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002080s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0011s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:09:55+08:00 ] ************** /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002083s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000404s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001451s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001994s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0012s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0007s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0006s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_notice` [ RunTime:0.0005s ]
SQL: SELECT COUNT(*) AS tp_count FROM `tw_notice` WHERE `uid` = 162 AND `status` = 1 LIMIT 1   [ RunTime:0.0006s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0005s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000114s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000229s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.015143s ]
INFO: [ view_parse ] --END-- [ RunTime:0.015213s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000180s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000245s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000436s ]
INFO: [ app_end ] --END-- [ RunTime:0.000512s ]

[ 2025-07-28T18:09:56+08:00 ] ************** /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002192s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000397s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001374s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001950s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:09:56+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000015s ]
INFO: [ app_init ] --END-- [ RunTime:0.002794s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000439s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001730s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002371s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:09:56+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.002934s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000497s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002158s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002833s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:09:56+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.002958s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000497s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002158s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002833s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:09:57+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002942s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000511s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002177s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002864s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:09:57+08:00 ] ************** /Ajaxtrade/obtain_usdz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002942s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000511s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002177s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002867s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_market` [ RunTime:0.0008s ]
SQL: SELECT `new_price`,`min_price`,`max_price`,`faxingjia`,`volume` FROM `tw_market` WHERE `name` = 'usdz_usdt' LIMIT 1   [ RunTime:0.0005s ]
NOTIC: [8] Undefined variable: cname /www/wwwroot/lumao.cc/Application/Mobile/Controller/AjaxtradeController.class.php 第 696 行.

[ 2025-07-28T18:09:57+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002942s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000440s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002493s ]
INFO: [ app_begin ] --END-- [ RunTime:0.003119s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:09:57+08:00 ] ************** /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.003022s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000540s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002208s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002932s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:09:57+08:00 ] ************** /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000017s ]
INFO: [ app_init ] --END-- [ RunTime:0.003144s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000540s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002356s ]
INFO: [ app_begin ] --END-- [ RunTime:0.003095s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:09:57+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.002298s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000382s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001684s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002215s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:09:58+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002883s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000434s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001535s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002115s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:09:58+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002291s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000456s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001634s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002253s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:09:58+08:00 ] ************** /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.001903s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000565s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002284s ]
INFO: [ app_begin ] --END-- [ RunTime:0.003095s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0012s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:09:59+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002103s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000389s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001457s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002018s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0013s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:00+08:00 ] ************** /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.003046s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000518s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002299s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002996s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:10:00+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.002968s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000457s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002261s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002894s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:01+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001904s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000352s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001346s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001835s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0013s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:02+08:00 ] ************** /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.002100s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000418s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001417s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001977s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0017s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0005s ]

[ 2025-07-28T18:10:02+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000023s ]
INFO: [ app_init ] --END-- [ RunTime:0.002379s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000440s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001721s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002342s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:03+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.002373s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000395s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001772s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002314s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0012s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0004s ]

[ 2025-07-28T18:10:03+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002099s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000406s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001516s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002080s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0015s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:04+08:00 ] ************** /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002104s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000510s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001456s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002182s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0012s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:10:04+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002198s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000409s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001601s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002156s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:10:05+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002099s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000339s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001393s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001866s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:10:05+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002240s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000405s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001701s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002269s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0011s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:10:06+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001990s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000449s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001389s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001978s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:06+08:00 ] ************** /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001942s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000342s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001426s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001906s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0011s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:06+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002440s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000453s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001909s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002520s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:10:07+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001918s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000338s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001359s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001827s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:14+08:00 ] ************** /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001999s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000401s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001472s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002026s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:10:15+08:00 ] ************** /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000017s ]
INFO: [ app_init ] --END-- [ RunTime:0.002289s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000386s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001507s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002066s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0006s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_notice` [ RunTime:0.0005s ]
SQL: SELECT COUNT(*) AS tp_count FROM `tw_notice` WHERE `uid` = 162 AND `status` = 1 LIMIT 1   [ RunTime:0.0007s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0006s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0003s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0003s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000140s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000318s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.021249s ]
INFO: [ view_parse ] --END-- [ RunTime:0.021380s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000212s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000309s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000481s ]
INFO: [ app_end ] --END-- [ RunTime:0.000623s ]

[ 2025-07-28T18:10:15+08:00 ] ************** /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002441s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000475s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001497s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002206s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:16+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002067s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000350s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001498s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002039s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:10:16+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.003015s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000553s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002306s ]
INFO: [ app_begin ] --END-- [ RunTime:0.003036s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0017s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0004s ]

[ 2025-07-28T18:10:16+08:00 ] ************** /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002230s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000361s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001637s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002150s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0004s ]

[ 2025-07-28T18:10:16+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002450s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000387s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001534s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002123s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:16+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002663s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000506s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001466s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002155s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:10:16+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002511s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000447s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001905s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002508s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:16+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002395s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000463s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001574s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002241s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:17+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002077s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000371s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001457s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001964s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0012s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:17+08:00 ] ************** /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002767s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000515s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002092s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002782s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:17+08:00 ] ************** /Ajaxtrade/obtain_usdz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002767s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000515s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002106s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002781s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_market` [ RunTime:0.0007s ]
SQL: SELECT `new_price`,`min_price`,`max_price`,`faxingjia`,`volume` FROM `tw_market` WHERE `name` = 'usdz_usdt' LIMIT 1   [ RunTime:0.0003s ]
NOTIC: [8] Undefined variable: cname /www/wwwroot/lumao.cc/Application/Mobile/Controller/AjaxtradeController.class.php 第 696 行.

[ 2025-07-28T18:10:17+08:00 ] ************** /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002240s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000354s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001517s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002011s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:10:18+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001963s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000348s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001595s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002085s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:18+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002364s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000353s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001682s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002175s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:19+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002235s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000392s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001572s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002104s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:10:19+08:00 ] 36.37.195.211 /User/index
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000015s ]
INFO: [ app_init ] --END-- [ RunTime:0.002593s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000418s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001418s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002009s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:10:20+08:00 ] ************** /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002036s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000361s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001523s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002050s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:10:20+08:00 ] 36.37.195.211 /Login/index
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.002934s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000498s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001986s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002744s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000048s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000131s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.005922s ]
INFO: [ view_parse ] --END-- [ RunTime:0.006011s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000173s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000238s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000420s ]
INFO: [ app_end ] --END-- [ RunTime:0.000552s ]

[ 2025-07-28T18:10:34+08:00 ] 36.37.195.211 /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.002392s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000484s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001715s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002375s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0004s ]

[ 2025-07-28T18:10:34+08:00 ] 36.37.195.211 /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002258s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000362s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001437s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001967s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0005s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0005s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000127s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000224s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.014911s ]
INFO: [ view_parse ] --END-- [ RunTime:0.014990s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000189s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000257s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000404s ]
INFO: [ app_end ] --END-- [ RunTime:0.000497s ]

[ 2025-07-28T18:10:35+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002130s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000366s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001699s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002237s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:10:35+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002004s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000336s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001480s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001952s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0005s ]

[ 2025-07-28T18:10:35+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000015s ]
INFO: [ app_init ] --END-- [ RunTime:0.002238s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000450s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002037s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002662s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:35+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002584s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000483s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001978s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002633s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0005s ]

[ 2025-07-28T18:10:36+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002551s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000431s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001672s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002255s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0013s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:36+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_usdz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002205s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000469s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001529s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002184s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_market` [ RunTime:0.0008s ]
SQL: SELECT `new_price`,`min_price`,`max_price`,`faxingjia`,`volume` FROM `tw_market` WHERE `name` = 'usdz_usdt' LIMIT 1   [ RunTime:0.0003s ]
NOTIC: [8] Undefined variable: cname /www/wwwroot/lumao.cc/Application/Mobile/Controller/AjaxtradeController.class.php 第 696 行.

[ 2025-07-28T18:10:36+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002216s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000420s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001690s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002264s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0011s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:36+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002364s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000453s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001653s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002307s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:10:36+08:00 ] 36.37.195.211 /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000015s ]
INFO: [ app_init ] --END-- [ RunTime:0.002984s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000480s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002244s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002920s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:10:36+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002979s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000474s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001706s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002359s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:10:37+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.003082s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000475s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001623s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002282s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:37+08:00 ] 36.37.195.211 /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001875s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000451s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001320s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001939s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0006s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0006s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0003s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000112s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000226s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.014305s ]
INFO: [ view_parse ] --END-- [ RunTime:0.014373s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000183s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000250s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000425s ]
INFO: [ app_end ] --END-- [ RunTime:0.000502s ]

[ 2025-07-28T18:10:38+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.003020s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000502s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002061s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002779s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0011s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:38+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002339s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000449s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001534s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002149s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:39+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_usdz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002397s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000409s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002015s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002598s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0012s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_market` [ RunTime:0.0010s ]
SQL: SELECT `new_price`,`min_price`,`max_price`,`faxingjia`,`volume` FROM `tw_market` WHERE `name` = 'usdz_usdt' LIMIT 1   [ RunTime:0.0004s ]
NOTIC: [8] Undefined variable: cname /www/wwwroot/lumao.cc/Application/Mobile/Controller/AjaxtradeController.class.php 第 696 行.

[ 2025-07-28T18:10:39+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.002450s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000496s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001802s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002501s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:39+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.002807s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000513s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002060s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002751s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:39+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.002725s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000513s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002059s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002750s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:10:39+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.002888s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000494s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002113s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002796s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0014s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:39+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000015s ]
INFO: [ app_init ] --END-- [ RunTime:0.002858s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000469s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001880s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002507s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:39+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.003066s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000554s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002158s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002909s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:10:40+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000016s ]
INFO: [ app_init ] --END-- [ RunTime:0.003250s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000576s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002340s ]
INFO: [ app_begin ] --END-- [ RunTime:0.003113s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:10:40+08:00 ] 36.37.195.211 /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.003361s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000506s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001901s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002620s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:10:41+08:00 ] 36.37.195.211 /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002354s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000393s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001744s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002291s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:10:41+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.002459s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000428s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001589s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002235s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:42+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002107s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000343s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001356s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001847s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:43+08:00 ] 36.37.195.211 /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000017s ]
INFO: [ app_init ] --END-- [ RunTime:0.002417s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000387s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001429s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001959s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0011s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:43+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002426s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000390s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001512s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002054s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:10:43+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002335s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000487s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001717s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002362s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:44+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000020s ]
INFO: [ app_init ] --END-- [ RunTime:0.003376s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000578s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002057s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002846s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:45+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002029s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000388s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001423s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001949s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0011s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:45+08:00 ] 36.37.195.211 /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002082s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000385s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001393s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001950s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0011s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:45+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002027s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000377s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001453s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001998s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:46+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002266s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000334s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001284s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001747s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:47+08:00 ] 36.37.195.211 /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000015s ]
INFO: [ app_init ] --END-- [ RunTime:0.002248s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000407s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001523s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002076s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:47+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001957s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000372s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001494s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002017s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:10:47+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002326s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000391s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001394s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001929s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:10:48+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001998s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000390s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001403s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001938s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:49+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002104s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000469s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002299s ]
INFO: [ app_begin ] --END-- [ RunTime:0.003100s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:10:49+08:00 ] 36.37.195.211 /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001977s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000361s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001426s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001939s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:49+08:00 ] 36.37.195.211 /Trade/index
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002043s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000354s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001459s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002029s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:10:49+08:00 ] 36.37.195.211 /Login/index
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002394s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000356s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001428s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001949s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000050s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000138s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.006822s ]
INFO: [ view_parse ] --END-- [ RunTime:0.006900s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000204s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000275s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000555s ]
INFO: [ app_end ] --END-- [ RunTime:0.000651s ]

[ 2025-07-28T18:11:07+08:00 ] 36.37.195.211 /Login/loginsubmit
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002146s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000335s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001443s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001943s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_user` [ RunTime:0.0007s ]
SQL: SELECT * FROM `tw_user` WHERE `username` = '123456' LIMIT 1   [ RunTime:0.0003s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.

[ 2025-07-28T18:11:21+08:00 ] 36.37.195.211 /Login/index
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002135s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000400s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001675s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002245s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000048s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000142s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.007024s ]
INFO: [ view_parse ] --END-- [ RunTime:0.007101s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000559s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000768s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000709s ]
INFO: [ app_end ] --END-- [ RunTime:0.000820s ]

[ 2025-07-28T18:12:56+08:00 ] ************** /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001971s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000336s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001301s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001762s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:12:56+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002054s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000400s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001421s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001961s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0011s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:12:56+08:00 ] ************** /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002188s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000426s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001476s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002052s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:12:56+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002387s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000345s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001677s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002188s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:12:57+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002759s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000454s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002060s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002709s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0011s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0004s ]

[ 2025-07-28T18:12:57+08:00 ] ************** /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002759s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000454s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002060s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002709s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:12:57+08:00 ] ************** /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002284s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000443s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001423s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002014s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:12:57+08:00 ] ************** /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.002325s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000422s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001492s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002074s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:12:57+08:00 ] ************** /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002186s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000399s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001506s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002097s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:12:57+08:00 ] ************** /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000016s ]
INFO: [ app_init ] --END-- [ RunTime:0.002255s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000345s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001772s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002264s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:12:58+08:00 ] ************** /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002056s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000433s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001557s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002169s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:12:58+08:00 ] ************** /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002814s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000497s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002170s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002846s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:12:58+08:00 ] ************** /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002814s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000497s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002170s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002846s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:12:58+08:00 ] ************** /Ajaxtrade/obtain_usdz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.002103s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000349s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001552s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002065s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0004s ]
SQL: SHOW COLUMNS FROM `tw_market` [ RunTime:0.0008s ]
SQL: SELECT `new_price`,`min_price`,`max_price`,`faxingjia`,`volume` FROM `tw_market` WHERE `name` = 'usdz_usdt' LIMIT 1   [ RunTime:0.0003s ]
NOTIC: [8] Undefined variable: cname /www/wwwroot/lumao.cc/Application/Mobile/Controller/AjaxtradeController.class.php 第 696 行.

[ 2025-07-28T18:12:58+08:00 ] ************** /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002050s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000442s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001455s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002054s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:12:59+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002386s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000403s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001656s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002235s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0004s ]

[ 2025-07-28T18:12:59+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002064s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000352s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001356s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001842s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0013s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:12:59+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002292s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000445s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001459s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002089s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:13:00+08:00 ] ************** /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002720s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000357s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002409s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002935s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:13:00+08:00 ] ************** /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.002315s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000389s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001498s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002069s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0011s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0004s ]

[ 2025-07-28T18:13:01+08:00 ] ************** /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002177s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000461s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001613s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002258s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:13:01+08:00 ] ************** /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002074s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000384s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001397s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001943s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:13:02+08:00 ] ************** /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.003224s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000552s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002230s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002982s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0011s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:16:13+08:00 ] 36.37.195.211 /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001975s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000410s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001453s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002006s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:16:14+08:00 ] 36.37.195.211 /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002229s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000338s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001390s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001868s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0005s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0005s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000113s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000205s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.014448s ]
INFO: [ view_parse ] --END-- [ RunTime:0.014535s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000194s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000275s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000474s ]
INFO: [ app_end ] --END-- [ RunTime:0.000558s ]

[ 2025-07-28T18:16:17+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002107s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000399s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001466s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002006s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:16:17+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_bch
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001881s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000329s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001435s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001905s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:16:17+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_ltc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002139s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000395s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001504s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002060s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:16:17+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_iota
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002188s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000392s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001396s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001927s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0004s ]

[ 2025-07-28T18:16:17+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_fil
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.003017s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000554s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002152s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002897s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:16:17+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_flow
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002043s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000374s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001426s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001952s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0011s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:16:18+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_jst
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001996s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000355s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001372s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001860s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:16:18+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_itc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002111s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000525s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001479s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002206s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:16:18+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_ht
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002274s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000424s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002102s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002706s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0010s ]

[ 2025-07-28T18:16:18+08:00 ] 36.37.195.211 /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002166s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000384s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001573s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002132s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:16:18+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_usdz
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002220s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000381s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001438s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001971s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_market` [ RunTime:0.0007s ]
SQL: SELECT `new_price`,`min_price`,`max_price`,`faxingjia`,`volume` FROM `tw_market` WHERE `name` = 'usdz_usdt' LIMIT 1   [ RunTime:0.0003s ]
NOTIC: [8] Undefined variable: cname /www/wwwroot/lumao.cc/Application/Mobile/Controller/AjaxtradeController.class.php 第 696 行.

[ 2025-07-28T18:16:19+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002396s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000492s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001673s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002421s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:16:19+08:00 ] 36.37.195.211 /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001897s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000338s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001314s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001823s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:16:20+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002157s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000330s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001406s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001874s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:16:21+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001983s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000326s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001278s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001734s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:16:21+08:00 ] 36.37.195.211 /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002029s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000383s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001398s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001922s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:16:22+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_eos
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.002171s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000442s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001495s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002108s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:16:23+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_eth
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002024s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000407s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001383s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001926s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:16:23+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002120s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000389s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001482s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002064s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:16:23+08:00 ] 36.37.195.211 /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002262s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000471s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001462s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002103s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:16:24+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_doge
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002001s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000379s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001809s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002344s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0005s ]

[ 2025-07-28T18:16:25+08:00 ] 36.37.195.211 /Ajaxtrade/get_market_one
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000015s ]
INFO: [ app_init ] --END-- [ RunTime:0.002205s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000418s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001549s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002136s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:16:26+08:00 ] 36.37.195.211 /Ajaxtrade/obtain_btc
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002382s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000428s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001932s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002526s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0011s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T18:16:26+08:00 ] 36.37.195.211 /Index/gglist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002253s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000396s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001674s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002217s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0005s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1  [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000049s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000150s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.006843s ]
INFO: [ view_parse ] --END-- [ RunTime:0.006926s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000212s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000283s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000596s ]
INFO: [ app_end ] --END-- [ RunTime:0.000673s ]

[ 2025-07-28T18:16:26+08:00 ] 36.37.195.211 /Index/gglist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.002908s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000535s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002143s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002917s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0007s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0004s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1  [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000050s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000170s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.006500s ]
INFO: [ view_parse ] --END-- [ RunTime:0.006576s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000196s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000261s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000484s ]
INFO: [ app_end ] --END-- [ RunTime:0.000694s ]

[ 2025-07-28T18:16:38+08:00 ] 36.37.195.211 /Index/gglist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002086s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000436s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001537s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002130s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0005s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1  [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000042s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000124s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.005712s ]
INFO: [ view_parse ] --END-- [ RunTime:0.005798s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000175s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000243s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000455s ]
INFO: [ app_end ] --END-- [ RunTime:0.000539s ]

[ 2025-07-28T18:16:38+08:00 ] 36.37.195.211 /Login/index
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.002108s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000377s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001447s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001993s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000053s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000142s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.006507s ]
INFO: [ view_parse ] --END-- [ RunTime:0.006579s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000203s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000292s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000416s ]
INFO: [ app_end ] --END-- [ RunTime:0.000519s ]

[ 2025-07-28T18:17:04+08:00 ] 36.37.195.211 /Index/gglist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001914s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000355s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001449s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001941s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0005s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1  [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000043s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000134s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.005904s ]
INFO: [ view_parse ] --END-- [ RunTime:0.005983s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000213s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000304s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000427s ]
INFO: [ app_end ] --END-- [ RunTime:0.000535s ]

[ 2025-07-28T18:31:18+08:00 ] 118.195.153.213 /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002173s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000383s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001356s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001875s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T18:31:19+08:00 ] 118.195.153.213 /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002113s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000369s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001490s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002011s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0005s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0005s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000106s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000197s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.014184s ]
INFO: [ view_parse ] --END-- [ RunTime:0.014255s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000179s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000248s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000415s ]
INFO: [ app_end ] --END-- [ RunTime:0.000492s ]

[ 2025-07-28T19:37:55+08:00 ] 78.153.140.203 /core/.env
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002126s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000480s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001586s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002232s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.

[ 2025-07-28T19:37:57+08:00 ] 78.153.140.203 /server/.env
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001828s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000352s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001302s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001776s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.

[ 2025-07-28T19:38:03+08:00 ] 78.153.140.203 /test
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002122s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000364s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001436s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001938s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.

[ 2025-07-28T19:38:05+08:00 ] 78.153.140.203 /.env.bak
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002146s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000385s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001670s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002209s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.

[ 2025-07-28T22:12:01+08:00 ] 49.51.245.241 /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002471s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000340s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001341s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001852s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T22:12:03+08:00 ] 49.51.245.241 /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002228s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000370s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001439s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001958s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0004s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0004s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0003s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000109s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000219s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.015011s ]
INFO: [ view_parse ] --END-- [ RunTime:0.015098s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000220s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000291s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000366s ]
INFO: [ app_end ] --END-- [ RunTime:0.000438s ]

[ 2025-07-28T22:20:02+08:00 ] 34.217.46.14 //cdn.staticfile.org/jquery/1.10.2/jquery.min.js
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001909s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000367s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001337s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001840s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.

[ 2025-07-28T22:20:02+08:00 ] 34.217.46.14 //cdn.staticfile.org/jquery/2.1.1/jquery.min.js
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002007s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000376s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001519s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002062s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.

[ 2025-07-28T22:27:05+08:00 ] 43.130.74.193 /User/online
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.002081s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000336s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001710s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002183s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T22:27:08+08:00 ] 43.130.74.193 /Login/index
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002015s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000565s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002263s ]
INFO: [ app_begin ] --END-- [ RunTime:0.003045s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000058s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000151s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.007018s ]
INFO: [ view_parse ] --END-- [ RunTime:0.007095s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000176s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000254s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000390s ]
INFO: [ app_end ] --END-- [ RunTime:0.000477s ]

[ 2025-07-28T22:35:36+08:00 ] 43.153.62.161 /Contract/index
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000013s ]
INFO: [ app_init ] --END-- [ RunTime:0.002751s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000516s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001942s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002657s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0011s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T22:35:38+08:00 ] 43.153.62.161 /Login/index
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001970s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000358s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001440s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001946s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0011s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000047s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000131s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.006989s ]
INFO: [ view_parse ] --END-- [ RunTime:0.007072s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000177s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000247s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000408s ]
INFO: [ app_end ] --END-- [ RunTime:0.000487s ]

[ 2025-07-28T22:45:37+08:00 ] 43.130.15.147 /Index/index
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.002204s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000359s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001423s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001922s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0011s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T22:45:40+08:00 ] 43.130.15.147 /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002308s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000496s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001587s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002279s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0005s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0006s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0005s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0003s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000125s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000214s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.016990s ]
INFO: [ view_parse ] --END-- [ RunTime:0.017078s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000208s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000277s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000393s ]
INFO: [ app_end ] --END-- [ RunTime:0.000470s ]

[ 2025-07-28T22:46:09+08:00 ] 93.159.230.28 /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.002317s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000361s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001375s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001878s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T22:46:10+08:00 ] 93.159.230.28 /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.001835s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000333s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001369s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001856s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0005s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0005s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0002s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000121s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000214s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.013703s ]
INFO: [ view_parse ] --END-- [ RunTime:0.013767s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000207s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000276s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000398s ]
INFO: [ app_end ] --END-- [ RunTime:0.000473s ]

[ 2025-07-28T22:54:39+08:00 ] 43.157.180.116 /User/index
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002291s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000478s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.002025s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002687s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T22:54:41+08:00 ] 43.157.180.116 /Login/index
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000012s ]
INFO: [ app_init ] --END-- [ RunTime:0.002511s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000514s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001716s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002408s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000082s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000232s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.011045s ]
INFO: [ view_parse ] --END-- [ RunTime:0.011159s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000271s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000418s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000576s ]
INFO: [ app_end ] --END-- [ RunTime:0.000710s ]

[ 2025-07-28T23:05:49+08:00 ] 124.156.179.141 /Login/setlang
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000011s ]
INFO: [ app_init ] --END-- [ RunTime:0.002134s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000404s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001403s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001966s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000045s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000126s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.005535s ]
INFO: [ view_parse ] --END-- [ RunTime:0.005612s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000259s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000346s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000433s ]
INFO: [ app_end ] --END-- [ RunTime:0.000509s ]

[ 2025-07-28T23:15:39+08:00 ] 43.156.109.53 /Index/gglist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.002287s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000381s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001742s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002274s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0005s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1  [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000048s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000146s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.006909s ]
INFO: [ view_parse ] --END-- [ RunTime:0.007016s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000202s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000287s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000595s ]
INFO: [ app_end ] --END-- [ RunTime:0.000688s ]

[ 2025-07-28T23:24:54+08:00 ] 129.226.174.80 /Login/findpwd
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002110s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000349s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001444s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001939s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000061s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000153s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.006835s ]
INFO: [ view_parse ] --END-- [ RunTime:0.006974s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000212s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000304s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000466s ]
INFO: [ app_end ] --END-- [ RunTime:0.000559s ]

[ 2025-07-28T23:31:31+08:00 ] 92.63.197.197 /
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002442s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000469s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001387s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002024s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]

[ 2025-07-28T23:36:34+08:00 ] 43.166.244.66 /Trade/index
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000014s ]
INFO: [ app_init ] --END-- [ RunTime:0.002206s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000500s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001450s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002121s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0011s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T23:36:37+08:00 ] 43.166.244.66 /Login/index
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.001959s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000361s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001400s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001896s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0012s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000046s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000127s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.006328s ]
INFO: [ view_parse ] --END-- [ RunTime:0.006403s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000171s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000235s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000384s ]
INFO: [ app_end ] --END-- [ RunTime:0.000462s ]

[ 2025-07-28T23:44:53+08:00 ] 43.157.149.188 /Orepool/index
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001960s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000388s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001336s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001862s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0010s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T23:44:56+08:00 ] 43.157.149.188 /Login/index
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000010s ]
INFO: [ app_init ] --END-- [ RunTime:0.002030s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000472s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001587s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002215s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0008s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000056s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000148s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.006726s ]
INFO: [ view_parse ] --END-- [ RunTime:0.006806s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000176s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000249s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000378s ]
INFO: [ app_end ] --END-- [ RunTime:0.000455s ]

[ 2025-07-28T23:54:32+08:00 ] 43.153.135.208 /Index/index/Lang/tr-tr
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000009s ]
INFO: [ app_init ] --END-- [ RunTime:0.002180s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000420s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001695s ]
INFO: [ app_begin ] --END-- [ RunTime:0.002310s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0009s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]

[ 2025-07-28T23:54:34+08:00 ] 43.153.135.208 /Trade/tradelist
INFO: [ app_init ] --START--
INFO: Run Behavior\BuildLiteBehavior [ RunTime:0.000008s ]
INFO: [ app_init ] --END-- [ RunTime:0.001993s ]
INFO: [ app_begin ] --START--
INFO: Run Behavior\ReadHtmlCacheBehavior [ RunTime:0.000394s ]
INFO: Run Behavior\CheckLangBehavior [ RunTime:0.001439s ]
INFO: [ app_begin ] --END-- [ RunTime:0.001979s ]
SQL: SHOW COLUMNS FROM `tw_config` [ RunTime:0.0012s ]
SQL: SELECT `webname` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0003s ]
SQL: SELECT `websildea`,`websildeb`,`websildec` FROM `tw_config` WHERE `id` = 1 LIMIT 1   [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_content` [ RunTime:0.0005s ]
SQL: SELECT `title`,`id` FROM `tw_content` WHERE `status` = 1 ORDER BY id desc  [ RunTime:0.0002s ]
SQL: SHOW COLUMNS FROM `tw_ctmarket` [ RunTime:0.0006s ]
SQL: SELECT `coinname`,`id`,`logo` FROM `tw_ctmarket` WHERE `status` = 1  [ RunTime:0.0003s ]
SQL: SELECT * FROM `tw_content` WHERE `status` = 1 ORDER BY id desc LIMIT 1   [ RunTime:0.0002s ]
INFO: [ view_parse ] --START--
INFO: [ template_filter ] --START--
INFO: Run Behavior\ContentReplaceBehavior [ RunTime:0.000113s ]
INFO: [ template_filter ] --END-- [ RunTime:0.000208s ]
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
NOTIC: [8192] preg_replace(): The /e modifier is deprecated, use preg_replace_callback instead /www/wwwroot/lumao.cc/ThinkPHP/Common/functions.php 第 157 行.
INFO: Run Behavior\ParseTemplateBehavior [ RunTime:0.014680s ]
INFO: [ view_parse ] --END-- [ RunTime:0.014779s ]
INFO: [ view_filter ] --START--
INFO: Run Behavior\WriteHtmlCacheBehavior [ RunTime:0.000249s ]
INFO: [ view_filter ] --END-- [ RunTime:0.000350s ]
INFO: [ app_end ] --START--
INFO: Run Behavior\ShowPageTraceBehavior [ RunTime:0.000481s ]
INFO: [ app_end ] --END-- [ RunTime:0.000584s ]

