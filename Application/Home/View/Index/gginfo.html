<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <meta name="viewport" content="width=device-width,minimum-scale=1,maximum-scale=1.0,initial-scale=1,user-scalable=no,viewport-fit=true" data-shuvi-head="true">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.5.0/dist/css/bootstrap.min.css" integrity="sha384-9aIt2nRpC12Uk9gS9baDl411NQApFmC26EwAOH8WgZl5MYYxFfc+NcPb1dKGj7Sk" crossorigin="anonymous">	
	    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.3.0/font/bootstrap-icons.css">
	    <link rel="stylesheet" type="text/css" href="/Public/Home/static/css/base.css" />
	    <title>{$webname}</title>
	    <style>
            .css-1wr4jig {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-box;
                display: -webkit-flex;
                display: -ms-flexbox;
                display: flex;
                -webkit-flex-direction: column;
                -ms-flex-direction: column;
                flex-direction: column;
                -webkit-flex: 1;
                -ms-flex: 1;
                flex: 1;
            }
            .css-wp2li4 {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-box;
                display: -webkit-flex;
                display: -ms-flexbox;
                display: flex;
                overflow-x: hidden;
                -webkit-flex-direction: column;
                -ms-flex-direction: column;
                flex-direction: column;
            }
            .css-19zx9ks {
                box-sizing: border-box;
                margin: 0;
                min-width: 0;
                display: -webkit-box;
                display: -webkit-flex;
                display: -ms-flexbox;
                display: flex;
                -webkit-align-items: center;
                -webkit-box-align: center;
                -ms-flex-align: center;
                align-items: center;
                height: 48px;
                font-size: 12px;
                color: #474D57;
                display: -webkit-box;
                display: -webkit-flex;
                display: -ms-flexbox;
                display: flex;
                min-height: 48px;
                height: -webkit-fit-content;
                height: -moz-fit-content;
                height: fit-content;
                -webkit-flex-wrap: wrap;
                -ms-flex-wrap: wrap;
                flex-wrap: wrap;
                z-index: 99;
                padding-top: 16px;
                padding-bottom: 10px;
                padding-left: 17%;
                padding-right: 17%;
                line-height: 1.25;
                box-shadow: 0px 2px 4px rgb(0 0 0 / 4%);
            }
            .css-4cffwv {
    box-sizing: border-box;
    margin: 0;
    min-width: 0;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
}
.css-vkw2w1 {
    box-sizing: border-box;
    margin: 0;
    min-width: 0;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    padding-left: 17%;
    padding-right: 40px;
    padding-top: 24px;
    padding-bottom: 0;
    width: 70%;
}
.css-kxziuu {
    box-sizing: border-box;
    margin: 0;
    min-width: 0;
    font-size: 20px;
    color: #1E2329;
    font-weight: 700;
}

.css-ivlfe5 {
    box-sizing: border-box;
    margin: 0;
    min-width: 0;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding-top: 24px;
    border-bottom: 1px solid #E6E8EA;
    padding-bottom: 24px;
}
.css-nqy1gg {
    box-sizing: border-box;
    margin: 0;
    min-width: 0;
    margin-right: 8px;
    padding-left: 4px;
    padding-right: 4px;
    padding-top: 4px;
    padding-bottom: 4px;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #1E2329;
    border: 1px solid #E6E8EA;
}
.css-178uoae {
    box-sizing: border-box;
    margin: 0;
    min-width: 0;
    color: #00b897;
    width: 24px;
    height: 24px;
    font-size: 24px;
    fill: #1E2329;
    fill: #00b897;
    width: 100%;
    height: 100%;
}
.css-vurnku {
    box-sizing: border-box;
    margin: 0;
    min-width: 0;
}
.css-ccbn3d {
    box-sizing: border-box;
    margin: 0;
    min-width: 0;
    color: #00b897;
    font-size: 14px;
}
.css-17s7mnd {
    box-sizing: border-box;
    margin: 0;
    min-width: 0;
    color: #000000;
    font-size: 12px;
}
.css-1ybfxxc {
    box-sizing: border-box;
    margin: 0;
    min-width: 0;
    overflow-wrap: break-word;
    padding-top: 24px;
    padding-bottom: 32px;
}
.css-1ii68zy {
    box-sizing: border-box;
    margin: 0;
    min-width: 0;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    font-weight: 400;
}

.css-3fpgoh {
    font-size: 16px;
    line-height: 28px;
    margin-bottom: 24px;
}
.css-3fpgoh {
    font-size: 16px;
    line-height: 28px;
    margin-bottom: 24px;
}
.css-3fpgoh {
    box-sizing: border-box;
    margin: 0;
    min-width: 0;
    font-size: 14px;
    line-height: 28px;
    margin-bottom: 16px;
    color: #1E2026;
}

            
            
	    </style>
	</head>
	<body>
	    <div class="App">
	        <div class="css-tq0shg">
	            <include file="Public:header"/>
	            
	            <main class="css-1wr4jig"  style="background: #fff;z-index: -1000;">
	                <div class="css-wp2li4">
	                    <div class="mirror-bread-crumb css-19zx9ks">
	                        <a class="css-o4bcl2">{:L('帮助中心')}</a>
	                    </div>
	                    <div class="css-4cffwv">
	                        <div class="css-vkw2w1">
	                            <h1 data-bn-type="text" class="css-kxziuu">{$info.title}</h1>
	                            <div class="css-ivlfe5">
	                               <div class="css-nqy1gg">
	                                   <img src="/Public/Home/static/imgs/alogn.png" class="css-178uoae">
	                                </div>
	                                <div class="css-vurnku">
	                                    <div class="css-ccbn3d">Usdz</div>
	                                    <div class="css-17s7mnd">{$info.addtime}</div>
	                                </div>
	                            </div>
	                            
	                            <div class="css-1ybfxxc">
	                                <article class="css-1ii68zy">
	                                    <?php if($info['img'] != 1){?>
	                                    <div class="css-3fpgoh">
	                                        <img src="/Upload/article/<?php echo $info['img'];?>" style="width: 100%;"/>
	                                    </div>
	                                    <?php }?>
	                                    <div class="css-3fpgoh">
	                                        {$info.content}
	                                    </div>
	                                </article>
	                            </div>
	                            
	                        </div>
	                    </div>

	                </div>
	            </main>

	            <include file="Public:footer"/>
	        </div>   
	    </div>
	    
	</body>
	<script src="https://cdn.staticfile.org/jquery/2.1.1/jquery.min.js"></script>
    <script type="text/javascript" src="/Public/Home/static/js/layer/layer.js" ></script>
    <script type="text/javascript" src="/Public/Home/static/js/jquery.SuperSlide.2.1.1.js" ></script>

    <script type="text/javascript">
		$("#nav").slide({ 
			type:"menu",// 效果类型，针对菜单/导航而引入的参数（默认slide）
			titCell:".nLi", //鼠标触发对象
			targetCell:".sub", //titCell里面包含的要显示/消失的对象
			effect:"slideDown", //targetCell下拉效果
			delayTime:300 , //效果时间
			triggerTime:0, //鼠标延迟触发时间（默认150）
			returnDefault:true //鼠标移走后返回默认状态，例如默认频道是“预告片”，鼠标移走后会返回“预告片”（默认false）
		});
	</script>

    
</html>