<!doctype html>
<html>
<head>
	<meta charset="UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<title>后台 | 管理中心 - ADMIN EX</title>
	<!-- Loading Bootstrap -->
	<link rel="stylesheet" type="text/css" href="__PUBLIC__/Admin/css/vendor/bootstrap/css/bootstrap.min.css">
	<link rel="stylesheet" type="text/css" href="__PUBLIC__/Admin/css/base.css" media="all">
	<link rel="stylesheet" type="text/css" href="__PUBLIC__/Admin/css/common.css" media="all">
	<link rel="stylesheet" type="text/css" href="__PUBLIC__/Admin/css/module.css">
	<link rel="stylesheet" type="text/css" href="__PUBLIC__/Admin/css/style.css" media="all">
	<link rel="stylesheet" type="text/css" href="__PUBLIC__/Admin/css/default_color.css" media="all">
	<script type="text/javascript" src="__PUBLIC__/Admin/js/jquery.min.js"></script>
	<script type="text/javascript" src="__PUBLIC__/layer/layer.js"></script>
	<link rel="stylesheet" type="text/css" href="__PUBLIC__/Admin/css/flat-ui.css">
	<script src="__PUBLIC__/Admin/js/flat-ui.min.js"></script>
	<script src="__PUBLIC__/Admin/js/application.js"></script>
</head>
<body>
<div class="navbar navbar-inverse navbar-fixed-top" role="navigation">
	<div class="navbar-header">
		<a class="navbar-brand" style="width:200px;text-align:center;background-color:#21202a;" href="{:U('Index/index')}">
			<img src="__PUBLIC__/Admin/ecshe_img/alogn.png" width="40" />
		</a>
	</div>
	<div class="navbar-collapse collapse">
		<ul class="nav navbar-nav">
			<!-- 主导航 -->
			<volist name="__MENU__.main" id="menu">
				<li <eq name="menu.class" value="current">class="active"</eq> > 
					<a href="{$menu.url|U}">
						{$menu.title} 
					</a>
				</li>
			</volist>
		</ul>
		<ul class="nav navbar-nav navbar-rights" style="margin-right:10px;">
			<li class="dropdown">
				<a href="#" class="dropdown-toggle" data-toggle="dropdown">
					 {:session('admin_username')}<b class="caret"></b>
				</a>
				<ul class="dropdown-menu">
					<li>
						<a href="{:U('User/setpwd')}">
							<span class="glyphicon glyphicon-wrench" aria-hidden="true"></span> 修改密码 
						</a>
					</li>
					<li class="center">
						<a href="javascript:void(0);" onclick="lockscreen()">
							<span class="glyphicon glyphicon-lock" aria-hidden="true"></span> 锁屏休息 
						</a>
					</li>
					<li class="dividers"></li>
					<li>
						<a href="{:U('Login/loginout')}">
							<span class="glyphicon glyphicon-log-out" aria-hidden="true"></span> 退出后台 
						</a>
					</li>
				</ul>
			</li>
			<li>
				<a href="{:U('Tools/delcache')}" class="dropdown-toggle" title="清除缓存">
					<span class="glyphicon glyphicon-trash" aria-hidden="true"></span>
				</a>
			</li>
			<li>
				<a class="dropdown-toggle" title="打开前台" href="__ROOT__/" target="_blank">
					<span class="glyphicon glyphicon-share" aria-hidden="true"></span>
				</a>
			</li>
		</ul>
	</div>
</div>
<!-- 边栏 -->
<div class="sidebar">
	<!-- 子导航 -->
	<block name="sidebar">
		<div id="subnav" class="subnav" style="max-height: 94%;overflow-x: hidden;overflow-y: auto;">
			<notempty name="_extra_menu">
				{// 动态扩展菜单 //} {:extra_menu($_extra_menu,$__MENU__)}
			</notempty>
			<volist name="__MENU__.child" id="sub_menu">
				<!-- 子导航 -->
				<notempty name="sub_menu">
					<notempty name="key"><h3><i class="icon icon-unfold"></i>{$key}</h3></notempty>
					<ul class="side-sub-menu">
						<volist name="sub_menu" id="menu">
							<li>
								<a class="item" href="{$menu.url|U}">
									<empty name="menu.ico_name">
										<span class="glyphicon glyphicon-share-alt" aria-hidden="true"></span>
										<else/>
										<span class="glyphicon glyphicon-{$menu.ico_name}" aria-hidden="true"></span>
									</empty>
									{$menu.title}
								</a>
							</li>
						</volist>
					</ul>
				</notempty>
				<!-- /子导航 -->
			</volist>
		</div>
	</block>
	<!-- /子导航 -->
</div>
<!-- /边栏 -->
<script type="text/javascript">
    $(function(){
        setInterval("tzfc()",2000);
    });
    
    function tzfc(){
        var st = 1;
        $.post("{:U('Admin/Trade/gethyorder')}",
        {'st':st},
        function(data){
            if(data.code == 1){
                layer.confirm('有新的合约订单', {
                  btn: ['知道了'] //按钮
                }, function(){
                    
                    $.post("{:U('Admin/Trade/settzstatus')}",
                    function(data){
                        if(data.code == 1){
                            window.location.reload();  
                        } 
                    });
                });
            }   
        });
    }
</script>



<eq name="versionUp" value="1">
	<script type="text/javascript" charset="utf-8">
		/**顶部警告栏*/
		var top_alert = $('#top-alerta');
		top_alert.find('.close').on('click', function () {
			top_alert.removeClass('block').slideUp(200);
			// content.animate({paddingTop:'-=55'},200);
		});
	</script>
</eq>