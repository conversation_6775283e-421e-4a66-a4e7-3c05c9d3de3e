<include file="Public:header" />
<div id="main-content">
	<div id="top-alert" class="fixed alert alert-error" style="display: none;">
		<button class="close fixed" style="margin-top: 4px;">&times;</button>
		<div class="alert-content">警告内容</div>
	</div>
	<div id="main" class="main">
		<div class="main-title-h">
			<span class="h1-title">服务器钱包信息</span>
			<span class="h2-title">>><a href="{:U('Coin/index')}">币种列表</a></span>
		</div>

		<div class="tab-wrap">

			<div class="tab-content">
				<form id="form" action="" method="post" class="form-horizontal">
					<div id="tab" class="tab-pane in tab">
						<div class="form-item cf">
							<table>
								<tr class="controls">
									<td class="item-label">钱包名称 :</td>
									<td></td>
									<td class="item-note">{$data.coin|default="0"}</td>
								</tr>
								<tr class="controls">
									<td class="item-label">钱包版本 :</td>
									<td></td>
									<td class="item-note">{$data.c.walletversion|default="0"}</td>
								</tr>
								<tr class="controls">
									<td class="item-label">钱包交易次数 :</td>
									<td></td>
									<td class="item-note">{$data.c.txcount|default="0"}</td>
								</tr>
								
								<tr class="controls">
									<td class="item-label">总区块 :</td>
									<td></td>
									<td class="item-note">{$data.a.headers|default="0"}</td>
								</tr>
								<tr class="controls">
									<td class="item-label">已经加载区块 :</td>
									<td></td>
									<td class="item-note">{$data.a.blocks|default="0"}</td>
								</tr>
								<tr class="controls">
									<td class="item-label">加载进度 :</td>
									<td></td>
									<td class="item-note"><eq name="data.a.blocks" value="$data['a']['headers']"> <font color="red">数据已加载完毕</font> <else /> 数据已加载:{$data['b']['verificationprogress']*100}%(加载完毕前不可交易) </eq></td>
								</tr>
								<tr class="controls">
									<td class="item-label">钱包总额 :</td>
									<td></td>
									<td class="item-note">{$data['b']['balance']}</td>
								</tr>
								<tr class="controls">
									<td class="item-label">站内总额 :</td>
									<td></td>
									<td class="item-note">{$data['num']}</td>
								</tr>
								
								
								<tr class="controls">
									<td class="item-label">当前费率 :</td>
									<td></td>
									<td class="item-note">{$data['b']['paytxfee']}</td>
								</tr>


								<volist name="data['b']" id="vo">

									<tr class="controls">
										<td class="item-label">{$key}:</td>
										<td></td>
										<td class="item-note">{$vo}</td>
									</tr>

								</volist>
							</table>
						</div>
					</div>
					<br>
				</form>
				<script type="text/javascript">
					//提交表单
					$('#submit').click(function(){
						$('#form').submit();
					});
				</script>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">
	$(function(){
		//主导航高亮
		$('.config-box').addClass('current');
		//边导航高亮
		$('.config-coin').addClass('current');
	});
</script>
<include file="Public:footer" />