<include file="Public:header" />
<div id="main-content">
	<div id="top-alert" class="fixed alert alert-error" style="display: none;">
		<button class="close fixed" style="margin-top: 4px;">&times;</button>
		<div class="alert-content">警告内容</div>
	</div>
	<div id="main" class="main">
		<div class="main-title-h">
			<span class="h1-title">钱包明细</span>
			<span class="h2-title">>><a href="{:U('Coin/index')}">币种列表</a></span>
		</div>

		<div class="cf">
			<div class="search-form fr cf">
				<div class="sleft">
				
				
				</div>
			</div>
		</div>
		<div class="data-table table-striped">
			<table class="">
				<thead>
					<tr>
						<th class="row-selected row-selected"><input class="check-all" type="checkbox" /></th>
						<th class="">ID</th>						
						<th class="">标签(用户名)</th>
						<th class="">钱包余额</th>
						<th class="">钱包地址</th>
						<th class="">站内余额</th>
						<th class="">站内地址</th>
						
					</tr>
				</thead>
				<tbody>
					<notempty name="list"> 
					
					<volist name="list" id="vo">
					<tr>
						<td><input class="ids" type="checkbox" name="id[]" value="{$vo.id}" /></td>
						<td>{$vo.id}</td>	
						<td>
						{$key}
						</td>		
						
						<td>{$vo.num}</td>
						<td>{$vo.addr}</td>
						<td>可用{$vo.xnb}<br>冻结{$vo.xnbd}<br>总额{$vo.zj}</td>
						<td>{$vo.xnbb}</td>
									
					</tr>
					</volist> <else />
					<td colspan="12" class="text-center empty-info"><i class="glyphicon glyphicon-exclamation-sign"></i>暂无数据</td>
					</notempty>
				</tbody>
			</table>
			<div class="page">
				<div>{$page}</div>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">
	//主导航高亮
	$('.config-box').addClass('current');
	$('.config-coin').addClass('current');
</script>
<include file="Public:footer" />