<include file="Public:header"/>
<div id="main-content">
	<div id="top-alert" class="fixed alert alert-error" style="display: none;">
		<button class="close fixed" style="margin-top: 4px;">&times;</button>
		<div class="alert-content">警告内容</div>
	</div>
	<div id="main" class="main">
		<div class="main-title-h">
			<span class="h1-title"><a href="{:U('Article/index')}">公告中心</a></span>
		</div>
		<div class="cf">
			<div class="fl">
				<a class="btn btn-success " href="{:U('Article/edit')}">新 增</a>
				<button class="btn ajax-post confirm btn-danger " url="{:U('Article/setstatus',array('type'=>'1'))}" target-form="ids">删 除</button>
			</div>
		</div>
		<div class="data-table table-striped">
			<table class="">
				<thead>
					<tr>
						<th class="row-selected row-selected"><input class="check-all" type="checkbox"/></th>
						<th class="">ID</th>
						<th class="">公告标题</th>
						<th class="">更新时间</th>
						<th class="">状态</th>
						<th class="">操作</th>
					</tr>
				</thead>
				<tbody>
					<notempty name="list">
						<volist name="list" id="vo">
							<tr>
								<td><input class="ids" type="checkbox" name="id[]" value="{$vo.id}"/></td>
								<td>{$vo.id}</td>
								<td>{$vo.title}</td>
								<td>{$vo.addtime}</td>
								<td><eq name="vo.status" value="1">显示<else/>隐藏</eq></td>
								<td><a href="{:U('Article/edit?id='.$vo['id'])}" class="btn btn-primary btn-xs">编辑</a></td>
							</tr>
						</volist>
					<else/>
						<td colspan="12" class="text-center empty-info"><i class="glyphicon glyphicon-exclamation-sign"></i>暂无数据</td>
					</notempty>
				</tbody>
			</table>
			<div class="page">
				<div>{$page}</div>
			</div>
		</div>
	</div>
</div>
<include file="Public:footer"/>
<block name="script">
	<script type="text/javascript" charset="utf-8">
		//导航高亮
		highlight_subnav("{:U('Article/index')}");
	</script>
</block>