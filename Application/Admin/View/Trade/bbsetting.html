<include file="Public:header" />

<div id="main-content">
    <div id="top-alert" class="fixed alert alert-error" style="display: none;">
		<button class="close fixed" style="margin-top: 4px;">&times;</button>
		<div class="alert-content">警告内容</div>
	</div>

	<div id="main" class="main">
		<div class="main-title-h">
			<span class="h1-title">币币交易参数设置</span>
		</div>

		<div class="tab-wrap">
			<div class="tab-content">
				<form id="form" action="{:U('Trade/bbsetting')}" method="post" class="form-horizontal" >
					<div id="tab" class="tab-pane in tab">
						<div class="form-item cf">
							<table>

								
								<tr class="controls">
									<td class="item-label" style="width:100px;">合约开市时间 :</td>
									<td style="width:300px;">
									    <p><input type="text" class="form-control input-10x" name="bb_kstime" value="{$info['bb_kstime']}"></p>
									    <p style="color:red;">填写格式如：00:00~24:00</p>
									</td>

								</tr>


                                <input type="hidden" name="bbid" value="{$info['id']}" />


								<tr class="controls">
									<td class="item-label"></td>
									<td>
										<div class="form-item cf">
											<button class= "btn submit-btn ajax-post"  target-form="form-horizontal" id="submit" type="submit">提交</button>
											<a class="btn btn-return" href="{$Think.server.HTTP_REFERER}">返 回</a>
										</div>
									</td>
								</tr>
							</table>
						</div>
					</div>
				</form>

				<script type="text/javascript">
					//提交表单
					$('#submit').click(function(){
						$('#form').submit();
					});
				</script>
			</div>
		</div>
	</div>
</div>


<script type="text/javascript">
	$(function(){
		//主导航高亮
		$('.config-box').addClass('current');
		//边导航高亮
		$('.config-contact').addClass('current');
	});
</script>

<include file="Public:footer" />