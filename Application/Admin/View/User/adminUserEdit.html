<include file="Public:header"/>
<div id="main-content">
    <div id="top-alert" class="fixed alert alert-error" style="display: none;">
        <button class="close fixed" style="margin-top: 4px;">&times;</button>
        <div class="alert-content">警告内容</div>
    </div>
    <div id="main" class="main">
        <div class="main-title-h">
            <span class="h1-title">编辑后台用户</span>
            <span class="h1-title">>><a href="{:U('User/adminUser')}">管理员列表</a></span>
        </div>

        <div class="tab-wrap">

            <div class="tab-content">
                <form id="form" action="{:U('User/adminUserEdit')}" method="post" class="form-horizontal">
                    <div id="tab" class="tab-pane in tab">
                        <div class="form-item cf">
                            <table>
                                <tr class="controls">
                                    <td class="item-label">用户名 :</td>
                                    <td><input type="text" class="form-control" name="username"
                                               value="{$data.username}"></td>
                                    <td class="item-note"></td>
                                </tr>

                                <tr class="controls">
                                    <td class="item-label">昵称 :</td>
                                    <td><input type="text" class="form-control" name="nickname"
                                               value="{$data.nickname}"></td>
                                    <td class="item-note"></td>
                                </tr>

                                <tr class="controls">
                                    <td class="item-label">登录密码 :</td>
                                    <td><input type="password" class="form-control" name="password" value=""></td>
                                </tr>


                                <tr class="controls">
                                    <td class="item-label">手机 :</td>
                                    <td><input type="text" class="form-control" name="mobile" value="{$data.mobile}"></td>
                                    <td class="item-note"></td>
                                </tr>

                                <tr class="controls">
                                    <td class="item-label">邮箱 :</td>
                                    <td><input type="text" class="form-control" name="email" value="{$data.email}"></td>
                                    <td class="item-note"></td>
                                </tr>

                                <tr class="controls">
                                    <td class="item-label">状态 :</td>
                                    <td><select name="status" class="input-small">
                                        <option value="1"
                                        <eq name="data.status" value="1">selected</eq>
                                        >正常</option>
                                        <option value="0"
                                        <eq name="data.status" value="0">selected</eq>
                                        >冻结</option>
                                    </select></td>
                                    <td class="item-note"></td>
                                </tr>


                                <tr class="controls">
                                    <td class="item-label"></td>
                                    <td>
                                        <div class="form-item cf">
                                            <button class="btn submit-btn ajax-post" id="submit" type="submit"
                                                    target-form="form-horizontal">提交
                                            </button>
                                            <a class="btn btn-return" href="{$Think.server.HTTP_REFERER}">返 回</a>
                                            <notempty name="data.id"><input type="hidden" name="id" value="{$data.id}"/>
                                            </notempty>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    //提交表单
    $('#submit').click(function () {
        $('#form').submit();
    });
</script>
<script type="text/javascript">
    $(function () {
        //主导航高亮
        $('.user-box').addClass('current');
        //边导航高亮
        $('.user-index').addClass('current');
    });
</script>
<include file="Public:footer"/>