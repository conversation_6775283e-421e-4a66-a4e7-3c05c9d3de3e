<include file="Public:header"/>
<div id="main-content">
    <div id="top-alert" class="fixed alert alert-error" style="display: none;">
        <button class="close fixed" style="margin-top: 4px;">&times;</button>
        <div class="alert-content">警告内容</div>
    </div>
    <div id="main" class="main">
        <div class="main-title-h">
            <span class="h1-title">后台用户管理</span>
            <notempty name="name">
                <span class="h2-title">>><a href="{:U('User/adminUser')}">管理列表</a></span>
            </notempty>
        </div>

        <div class="cf">
            <div class="fl">
                <a class="btn btn-success navbar-btn btn-sm" href="{:U('User/adminUserEdit')}">新 增</a>
                <button class="btn ajax-post btn-info navbar-btn btn-sm"
                        url="{:U('User/status',array('type'=>'resume','mobile'=>'Admin'))}" target-form="ids">启 用
                </button>
                <button class="btn ajax-post btn-warning navbar-btn btn-sm"
                        url="{:U('User/status',array('type'=>'forbid','mobile'=>'Admin'))}" target-form="ids">禁 用
                </button>
                <button class="btn ajax-post confirm btn-danger navbar-btn btn-sm" confirm-msg="批量删除不可恢复,确定删除?"
                        url="{:U('User/status',array('type'=>'del','mobile'=>'Admin'))}" target-form="ids">删 除
                </button>
            </div>


            <div class="search-form fr cf">
                <div class="sleft">
                    <form name="formSearch" id="formSearch" method="get" name="form1">
                        <select style="height: 32px; font-size: 14px; width: 100px; float: left; margin-right: 5px;"
                                name="field">
                            <option value="uid"
                            <option value="username">用户名</option>
                        </select> <input type="text" name="name" class="search-input" value="{$name}"
                                         placeholder="查找管理员" style="width: 150px">
                        <a class="sch-btn" href="javascript:;" id="search">
                            <i class="btn-search"></i>
                        </a>
                    </form>
                </div>
            </div>
        </div>
        <div class="data-table table-striped">
            <table class="">
                <thead>
                <tr>
                    <th class="row-selected row-selected"><input class="check-all" type="checkbox"/></th>
                    <th class="">ID</th>
                    <th class="">用户名</th>
                    <th class="">真实姓名</th>
                    <th class="">手机号</th>
                    <th class="">邮箱</th>
                    <th class="">状态</th>
                    <th class="">操作</th>
                </tr>
                </thead>
                <tbody>
                <notempty name="list">
                    <volist name="list" id="vo">
                        <tr>
                            <td><input class="ids" type="checkbox" name="id[]" value="{$vo.id}"/></td>
                            <td>{$vo.id}</td>
                            <td>{$vo.username}</td>
                            <td>{$vo.nickname}</td>
                            <td>{$vo.mobile}</td>
                            <td>{$vo.email}</td>
                            <td>
                                <eq name="vo.status" value="0">禁用</eq>
                                <eq name="vo.status" value="1">正常</eq>
                            </td>
                            <td>
                                <a href="{:U('User/adminUserEdit?id='.$vo['id'])}" class="btn btn-info btn-xs">编辑</a>
                            </td>
                        </tr>
                    </volist>
                    <else/>
                    <td colspan="12" class="text-center empty-info"><i class="glyphicon glyphicon-exclamation-sign"></i>暂无数据</td>
                </notempty>
                </tbody>
            </table>
            <div class="page">
                <div>{$page}</div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    function del_adminuser() {
        var aaa = confirm("确定要清空数据吗？");
        alert(aaa);
        return false;

    }
    //搜索功能
    $(function () {
        $('#search').click(function () {
            $('#formSearch').submit();
        });
    });
    //回车搜索
    $(".search-input").keyup(function (e) {
        if (e.keyCode === 13) {
            $("#search").click();
            return false;
        }
    });
</script>
<script type="text/javascript">
    $(function () {
        //主导航高亮
        $('.user-box').addClass('current');
        //边导航高亮
        $('.user-index').addClass('current');
    });
</script>
<include file="Public:footer"/>