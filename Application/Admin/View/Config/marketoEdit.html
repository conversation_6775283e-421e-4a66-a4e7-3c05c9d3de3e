<include file="Public:header"/>
<div id="main-content">
	<div id="top-alert" class="fixed alert alert-error" style="display: none;">
		<button class="close fixed" style="margin-top: 4px;">&times;</button>
		<div class="alert-content">警告内容</div>
	</div>
	<div id="main" class="main">
		<div class="main-title-h">
			<span class="h1-title"><a href="{:U('Config/marketo')}">市场列表</a> &gt;&gt;</span>
            <span class="h1-title"><empty name="data">新增市场<else/>编辑市场</empty></span>
		</div>
		<div class="tab-wrap">
			<div class="tab-content">
				<form id="form" action="{:U('Config/marketoEdit')}" method="post" class="form-horizontal" enctype="multipart/form-data">
					<div id="tab" class="tab-pane in tab">
						<div class="form-item cf">
							<table>
	
								<tr class="controls">
									<td class="item-label">当前交易对 :</td>
									<td>{$data.name}</td>
									<td class="item-note"></td>
								</tr>
								

								<tr class="controls">
									<td class="item-label">交易价 - 小数位数:</td>
									<td>
                                        <empty name="data['round_mum']">
											<input type="text" class="form-control input-10x" name="round_mum" value="2">
										<else />
											<input type="text" class="form-control input-10x" name="round_mum" value="{$data.round_mum}">
                                        </empty>
									</td>
									<td class="item-note" style="color:red;">填1--6整数，比如填3就表示3位小数</td>
								</tr>
								<tr class="controls">
									<td class="item-label">交易数量 - 小数位数:</td>
									<td>
                                        <empty name="data['round']">
											<input type="text" class="form-control input-10x" name="round" value="4">
										<else />
											<input type="text" class="form-control input-10x" name="round" value="{$data.round}">
                                        </empty>
									</td>
									<td class="item-note" style="color:red;">填1--6整数，比如填3就表示3位小数</td>
								</tr>


								<tr class="controls">
									<td class="item-label">买入最小交易价:</td>
									<td>
                                        <empty name="data['buy_min']">
											<input type="text" class="form-control input-10x" name="buy_min" value="0.0001">
										<else />
											<input type="text" class="form-control input-10x" name="buy_min" value="{$data.buy_min}">
                                        </empty>
									</td>
									<td class="item-note" style="color:red;">买入最小交易价 默认0.000001</td>
								</tr>
								<tr class="controls">
									<td class="item-label">买入<b style="color:#019CFF;">最大</b>交易价:</td>
									<td>
                                        <empty name="data['buy_max']">
											<input type="text" class="form-control input-10x" name="buy_max" value="10000000">
										<else />
											<input type="text" class="form-control input-10x" name="buy_max" value="{$data.buy_max}">
                                        </empty>
									</td>
									<td class="item-note" style="color:red;">买入最大交易价 默认10000000</td>
								</tr>
								<tr class="controls">
									<td class="item-label">卖出最小交易价:</td>
									<td>
                                        <empty name="data['sell_min']">
											<input type="text" class="form-control input-10x" name="sell_min" value="0.0001">
										<else />
											<input type="text" class="form-control input-10x" name="sell_min" value="{$data.sell_min}">
                                        </empty>
									</td>
									<td class="item-note" style="color:red;">卖出最小交易价 默认0.000001</td>
								</tr>
								<tr class="controls">
									<td class="item-label">卖出<b style="color:#019CFF;">最大</b>交易价:</td>
									<td>
                                        <empty name="data['sell_max']">
											<input type="text" class="form-control input-10x" name="sell_max" value="10000000">
										<else />
											<input type="text" class="form-control input-10x" name="sell_max" value="{$data.sell_max}">
                                        </empty>
									</td>
									<td class="item-note" style="color:red;">卖出最大交易价 默认10000000</td>
								</tr>
								
								<tr class="controls">
									<td class="item-label">单笔最小交易额:</td>
									<td>
                                        <empty name="data['trade_min']">
											<input type="text" class="form-control input-10x" name="trade_min" value="0.0001">
										<else />
											<input type="text" class="form-control input-10x" name="trade_min" value="{$data.trade_min}">
                                        </empty>
									</td>
									<td class="item-note" style="color:red;">单笔最小交易额 默认10000000</td>
								</tr>
								<tr class="controls">
									<td class="item-label">单笔<b style="color:#019CFF;">最大</b>交易额:</td>
									<td>
                                        <empty name="data['trade_max']">
											<input type="text" class="form-control input-10x" name="trade_max" value="10000000">
										<else />
											<input type="text" class="form-control input-10x" name="trade_max" value="{$data.trade_max}">
                                        </empty>
									</td>
									<td class="item-note" style="color:red;">单笔最大交易额 默认10000000</td>
								</tr>

								<tr class="controls">
									<td class="item-label">单笔买入最小交易数量:</td>
									<td>
                                        <empty name="data['trade_buy_num_min']">
											<input type="text" class="form-control input-10x" name="trade_buy_num_min" value="0.0001">
										<else />
											<input type="text" class="form-control input-10x" name="trade_buy_num_min" value="{$data.trade_buy_num_min}">
                                        </empty>
									</td>
									<td class="item-note" style="color:red;">单笔买入最小交易量 默认0.0001</td>
								</tr>
								<tr class="controls">
									<td class="item-label">单笔买入<b style="color:#019CFF;">最大</b>交易数量:</td>
									<td>
                                        <empty name="data['trade_buy_num_max']">
											<input type="text" class="form-control input-10x" name="trade_buy_num_max" value="10000000">
										<else />
											<input type="text" class="form-control input-10x" name="trade_buy_num_max" value="{$data.trade_buy_num_max}">
                                        </empty>
									</td>
									<td class="item-note" style="color:red;">单笔买入最大交易量 默认1000</td>
								</tr>
								<tr class="controls">
									<td class="item-label">单笔卖出最小交易数量:</td>
									<td>
                                        <empty name="data['trade_sell_num_min']">
                                                <input type="text" class="form-control input-10x" name="trade_sell_num_min" value="0.0001">
                                            <else />
                                                <input type="text" class="form-control input-10x" name="trade_sell_num_min" value="{$data.trade_sell_num_min}">
                                        </empty>
									</td>
									<td class="item-note" style="color:red;">单笔卖出最小交易量 默认0.0001</td>
								</tr>
								<tr class="controls">
									<td class="item-label">单笔卖出<b style="color:#019CFF;">最大</b>交易数量:</td>
									<td>
                                       <empty name="data['trade_sell_num_max']">
											<input type="text" class="form-control input-10x" name="trade_sell_num_max" value="10000000">
										<else />
											<input type="text" class="form-control input-10x" name="trade_sell_num_max" value="{$data.trade_sell_num_max}">
                                        </empty>
									</td>
									<td class="item-note" style="color:red;">单笔卖出最大交易量 默认1000</td>
								</tr>

								<tr class="controls">
									<td class="item-label">涨幅限制:</td>
									<td>
										<input type="text" class="form-control input-10x" name="zhang" value="{$data.zhang}">
									</td>
									<td class="item-note" style="color:red;">% 根据昨日最后一笔交易计算</td>
								</tr>
								<tr class="controls">
									<td class="item-label">跌幅限制:</td>
									<td>
										<input type="text" class="form-control input-10x" name="die" value="{$data.die}">
									</td>
									<td class="item-note" style="color:red;">% 根据昨日最后一笔交易计算</td>
								</tr>


								<tr class="controls">
									<td class="item-label">开启交易 :</td>
									<td><select name="trade" class="form-control input-10x">
										<option value="1"
										<eq name="data.trade" value="1">selected</eq>
										>开启交易</option>
										<option value="0"
										<eq name="data.trade" value="0">selected</eq>
										>禁止交易</option>
									</select></td>
									<td class="item-note"></td>
								</tr>
								<tr class="controls">
									<td class="item-label">排序:</td>
									<td>
										<input type="text" class="form-control input-10x" name="sort" value="{$data.sort}">
									</td>
									<td class="item-note"></td>
								</tr>
								<tr class="controls">
									<td class="item-label">状态 :</td>
									<td><select name="status" class="form-control input-10x">
										<option value="1"
										<eq name="data.status" value="1">selected</eq>
										>可用</option>
										<option value="0"
										<eq name="data.status" value="0">selected</eq>
										>禁用</option>
									</select></td>
									<td class="item-note"></td>
								</tr>
								<tr class="controls">
									<td class="item-label"></td>
									<td>
										<div class="form-item cf">
											<button class="btn submit-btn ajax-post" id="submit" type="submit" target-form="form-horizontal">提交
											</button>
											<a class="btn btn-return" href="{$Think.server.HTTP_REFERER}">返 回</a>
											<notempty name="data.id"><input type="hidden" name="id" value="{$data.id}"/>
											</notempty>
										</div>
									</td>
								</tr>
							</table>
						</div>
					</div>
				</form>
				<script type="text/javascript">
				//提交表单
				$('#submit').click(function () {
					$('#form').submit();
				});
				</script>
			</div>
		</div>
	</div>
</div>
<include file="Public:footer"/>
<block name="script">
	<script type="text/javascript" charset="utf-8">
		//导航高亮
		highlight_subnav("{:U('Config/marketo')}");
	</script>
</block>