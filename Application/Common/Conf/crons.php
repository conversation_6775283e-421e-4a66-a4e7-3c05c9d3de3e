<?php
return array(
    // 5-10秒执行一次的任务
    'setwl' => array('setwl.php', 5, 0), // 设置成5秒执行一次
    'setwl_ty' => array('setwl_ty.php', 5, 0), // 设置成5秒执行一次
    'autoxjtade' => array('autoxjtade.php', 10, 0), // 委托订单自动交易，10秒执行一次
    'hycarryout_ty' => array('hycarryout_ty.php', 5, 0), // 自动结算体验合约订单，5秒执行一次
    'hycarryout' => array('hycarryout.php', 5, 0), // 自动结算合约订单，5秒执行一次
    
    // 每天执行一次的任务（86400秒 = 24小时）
    'autokjsy' => array('autokjsy.php', 86400, 0), // 独资矿机自动收益，每天执行一次
    'releasedjprofit' => array('releasedjprofit.php', 86400, 0), // 释放冻结的矿机收益币，每天执行一次
    'authsharesjsy' => array('authsharesjsy.php', 86400, 0), // 共享矿机自动结算收益，每天执行一次
    'releaseissue' => array('releaseissue.php', 86400, 0), // 自动释放冻结的认购币，每天执行一次
    'rengoubimoneyup' => array('rengoubimoneyup.php', 86400, 0), // 认购币每日增长，每天00:00执行
    'rengoubijiesuan' => array('rengoubijiesuan.php', 86400, 0), // 认购币满XX天结算，每天00:00执行
    'rengoubifafang' => array('rengoubifafang.php', 86400, 0), // 发放认购币波动利润，每天00:00执行
    'fortrade' => array('fortrade.php', 86400, 0), // 系统币种计算交易，每天一次
    'chart' => array('chart.php', 86400, 0), // 系统币行情计算，每天一次
);
?> 