<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.5.0/dist/css/bootstrap.min.css" integrity="sha384-9aIt2nRpC12Uk9gS9baDl411NQApFmC26EwAOH8WgZl5MYYxFfc+NcPb1dKGj7Sk" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.3.0/font/bootstrap-icons.css">
    <link rel="stylesheet" type="text/css" href="/Public/Static/css/base.css" />
    <link rel="stylesheet" type="text/css" href="/Public/Static/css/nologed.css" />

    <title>{$webname}</title>
    <style>
        ::-webkit-input-placeholder { /* WebKit browsers */
            color: #b5b5b5;
            font-size: 14px;
        }

        ::-moz-placeholder { /* Mozilla Firefox 19+ */
            color: #b5b5b5;
            font-size: 14px;
        }
        input:focus{background:#F5F5F5;outline: 1px solid #F5F5F5;}
        a:hover,a:link,a:visited,a:active{color:#FCD535;text-decoration:none;}



        .lbox {
            width: 100%;
            height: 60px;
        }

        .alltn {
            width: 100%;
            height: 40px;
            line-height: 40px;
            text-align: center;
            border-radius: 5px;
            background: #0052fe;
            color: #fff;
        }

        .header_box {
            background: #f5f5f5;
        }
        .container-fluid {
            background: url("/Public/Static/img/background-1-2.svg") no-repeat;
            background-size: cover;
        }

    </style>
</head>
<body>
<div class="no_header header_box" >
    <div class="fl bhalf allhg txtl" style="line-height:50px;padding-right: 20px;">
        <i class="bi bi-x fw"  onclick="goindex()" style="font-size:36px;"></i>
    </div>
    <div class="fr bhalf allhg txtr" style="line-height:50px;padding-right: 20px;">
        <a href="{:U('Login/register')}" class="fzmmm" style="color: #0052fe">{:L('注册')}</a>
    </div>
</div>
<div class="container-fluid ctbox">


    <div class="no_content">
        <div class="no_title">
            <span class="title_txt fch">{:L('欢迎登录Robit')}</span>
        </div>

        <div class="no_inbox">
            <div class="inputbox">
                <div class="input_title txtl">
                    <span class="fzmmm ">{:L('登录邮箱或手机号')}</span>
                </div>
                <div class="input_div">
                    <input type="text" style="height:45px;" class="cinput" name="email" id="email" placeholder="{:L('请输入登录邮箱或手机号')}" />
                </div>
            </div>

            <div class="inputbox">
                <div class="input_title txtl">
                    <span class="fzmmm ">{:L('登录密码')}</span>
                </div>
                <div class="input_div">
                    <input type="password" style="height:45px;" class="cinput" name="lpwd" id="lpwd" placeholder="{:L('请输入密码')}" />
                </div>
            </div>

            <div class="lbox" onclick="uplogin();">
                    <div class="alltn" style="margin-top:20px;">
                        <span class="fzmmm">{:L('登录')}</span>
                    </div>
            </div>


            <div class="input_title txtr">
                <a href="{:U('Login/findpwd')}" class="fzmmm" style="color: #0052fe">{:L('忘记密码')}?</a>
            </div>

        </div>

    </div>
</div>
</body>

<body>
<script src="https://cdn.staticfile.org/jquery/2.1.1/jquery.min.js"></script>
<script type="text/javascript" src="/Public/Static/js/layer/layer.js" ></script>
<script type="text/javascript">
    function uplogin(){
        var email = $("#email").val();
        var reg = /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/;
        if(email=='' || email == null){
            layer.msg("{:L('请输入邮箱')}");return false;
        }
        if(!reg.test(email)){
            layer.msg("{:L('邮箱格式不正确')}");return false;
        }
        var lpwd = $("#lpwd").val();
        if(lpwd == ''){
            layer.msg("{:L('请输入密码')}");return false;
        }
        $.post("{:U('Login/loginsubmit')}",
            {'email' : email,  'lpwd' : lpwd},
            function(data){
                if(data.code){
                    layer.msg(data.info);
                    setTimeout(function(){
                        window.location.href="{:U('Index/index')}";
                    },2000);
                }else{
                    layer.msg(data.info);return false;
                }
            });

    }
</script>


<script type="text/javascript">
    function goindex(){
        window.location.href="{:U('Index/index')}";
    }
</script>
</html>



