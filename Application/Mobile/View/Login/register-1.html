<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.5.0/dist/css/bootstrap.min.css" integrity="sha384-9aIt2nRpC12Uk9gS9baDl411NQApFmC26EwAOH8WgZl5MYYxFfc+NcPb1dKGj7Sk" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.3.0/font/bootstrap-icons.css">
    <link rel="stylesheet" type="text/css" href="/Public/Static/css/base.css" />
    <link rel="stylesheet" type="text/css" href="/Public/Static/css/nologed.css" />
    <title>{$webname}</title>
    <style>
        ::-webkit-input-placeholder { /* WebKit browsers */
            color: #b5b5b5;
            font-size: 18px;
        }

        ::-moz-placeholder { /* Mozilla Firefox 19+ */
            color: #b5b5b5;
            font-size: 18px;
        }
        input:focus{background:#F5F5F5;outline: 1px solid #F5F5F5;}
        a:hover,a:link,a:visited,a:active{color:#000;text-decoration:none;}
        .boxh{height: 90px}
        .smsbtn{width:35%;float:right;height:50px;line-height:50px;border-radius:5px;background: #F5F5F5;text-align: center;}


        .header_box {
            background: #f5f5f5;
        }


        .alltn {
            width: 100%;
            height: 40px;
            line-height: 40px;
            text-align: center;
            border-radius: 5px;
            background: #0052fe;
            color: #fff;
        }
        .lbox {
            width: 100%;
            height: 60px;
            margin-bottom: 20px;
        }

    </style>

    <link rel="stylesheet" href="/Public/Static/bootstrap/bootstrap.min.css">
    <script src="/Public/Static/bootstrap/jquery.min.js"></script>
    <script src="/Public/Static/bootstrap/bootstrap.min.js"></script>



</head>
<body>

<div class="no_header header_box" >
    <div class="fl bhalf allhg txtl" style="line-height:50px;padding-right: 20px;">
        <i class="bi bi-x fw"  onclick="goindex()" style="font-size:36px;"></i>
    </div>
    <div class="fr bhalf allhg txtr" style="line-height:50px;padding-right: 20px;">
        <a href="{:U('Login/index')}" class="fzmmm" style="color: #0052fe">{:L('登录')}</a>
    </div>
</div>


<div class="container-fluid ctbox">
    <div class="no_content">
        <div class="no_title">
            <span class="title_txt fch">{:L('注册')}</span>
        </div>

        <ul class="nav nav-pills nav-justified">
            <li class="active" onclick="selection_box('phone')" id="sphone"><a href="#">手机号码</a></li>
            <li class="" onclick="selection_box('email')" id="semail"><a href="#">邮箱</a></li>
            <input type="hidden" name="type" id="type" value="1" />

        </ul>
        <div class="no_inbox" style="display: block" id="phone_box">
            <div class="inputbox boxh">
                <div class="input_title txtl">
                    <span class="fzmm fcc">{:L('手机号码')}</span>
                </div>
                <div class="input_div">
                    <input type="text"  style="height:45px;" class="cinput" name="phone" id="phone" placeholder="{:L('请输入手机号')}" />
                </div>
            </div>
        </div>

        <div class="no_inbox" style="display: none" id="email_box">
            <div class="inputbox boxh">
                <div class="input_title txtl">
                    <span class="fzmm fcc">{:L('邮箱')}</span>
                </div>
                <div class="input_div">
                    <input type="text"  style="height:45px;" class="cinput" name="email" id="email" placeholder="{:L('请输入邮箱')}" />
                </div>
            </div>
        </div>

    </div>

    <div class="no_button_box">

        <div class="" style="line-height: 25px;">
            <span>{:L('注册即表示同意')}&nbsp;&nbsp;</span>
            <a href="##" class="fzmm fcy">{:L('用户服务协议')}</a>
        </div>
        <div class="lbox" onclick="next()">
            <div class="alltn" style="">
                <span class="fzmmm">{:L('下一步')}</span>
            </div>
        </div>
    </div>



</div>
</body>

<body>
<script src="https://cdn.staticfile.org/jquery/2.1.1/jquery.min.js"></script>
<script type="text/javascript" src="/Public/Static/js/layer/layer.js" ></script>
<script type="text/javascript">


    function selection_box(type)
    {
        if (type == 'phone') {
            $('#sphone').addClass('active');
            $('#semail').removeClass('active');
            $('#phone_box').show();
            $('#email_box').hide();
            $('#type').val(1);
        }

        if (type == 'email') {
            $('#sphone').removeClass('active');
            $('#semail').addClass('active');
            $('#phone_box').hide();
            $('#email_box').show();
            $('#type').val(2);
        }
    }


    function next() {
        var type = $('#type').val();
        if (type == 1) {
            var  account = $('#phone').val();
            if(account=='' || account == null){
                layer.msg("{:L('请输入手机号码')}");return false;
            }
        }else {
            var  account = $('#email').val();
            var reg = /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/;
            if(account=='' || account == null){
                layer.msg("{:L('请输入邮箱')}");return false;
            }
            if(!reg.test(account)){
                layer.msg("{:L('邮箱格式不正确')}");return false;
            }
        }
        var path = '/Login/register?type='+type+'account='+account
        window.location.href = path;

    }

</script>
<script type="text/javascript">

</script>
<script type="text/javascript">
    function goindex(){
        window.location.href="{:U('Index/index')}";
    }
</script>


</html>



