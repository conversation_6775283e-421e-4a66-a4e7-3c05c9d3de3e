<!DOCTYPE html>
<html lang="zh-CN" style="background:#fff;">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.5.0/dist/css/bootstrap.min.css" integrity="sha384-9aIt2nRpC12Uk9gS9baDl411NQApFmC26EwAOH8WgZl5MYYxFfc+NcPb1dKGj7Sk" crossorigin="anonymous">	
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.3.0/font/bootstrap-icons.css">
	<link rel="stylesheet" type="text/css" href="/Public/Static/css/base2.css" />
	<link rel="stylesheet" type="text/css" href="/Public/Static/css/nologed.css" />
	<link rel="stylesheet" href="/Public/Static/Icoinfont/iconfont.css">
	<script src="/Public/Static/Icoinfont/iconfont.js"></script>
	<title>{$webname}</title>
	<style>
	    ::-webkit-input-placeholder {color: #b5b5b5;font-size: 12px;}
	    ::-moz-placeholder {color: #b5b5b5;font-size: 12px;}
	    input:focus{background:#f5f5f5;outline: 1px solid #f5f5f5;}
	    a:hover,a:link,a:visited,a:active{color:#000000;text-decoration:none;}
	    .no_header{position: fixed;z-index: 9999;padding:0px 10px;top:0px;line-height: 50px;background:#1b1d29;}
	    .contentbox{width:100%;margin-top:45px;}
	    .contentbox_top{width:100%;height:50px;line-height:50px;text-align:left;padding:0px 15px;}
        .listbox{width:100%;min-height:100px;background:#121420;padding:5px 15px;}
        .listbox_title{width:100%;height:30px;line-height:30px;}
        .listbox_title_l{width:50%;height:30px;float:left;text-align:left;}
        .listbox_title_r{width:50%;height:30px;float:right;text-align:right;}
        .titlebox{width:29%;height:50px;line-height:50px;float:left;}
        .xztitile{color:#000;font-size:22px;font-weight:bold;}
        .notitile{color:#000000;font-size:20px;font-weight:500px;}
        .listbox{width:100%;background:#121420;margin-top:100px;}
        .tlistbox{width:100%;clear:both;padding:10px 0px;}
	    .tlistbox_1{
			width: 100%;
			padding: 5px 10px;
			background: #2C3445;
			border-radius: 10px;
		}
	    .tlistbox_2{width:100%;height:30px;}
	    .tlistbox_3{width:80%;height:30px;line-height:30px;}
	    .tlistbox_4{width:20%;height:30px;line-height:30px;}
	    .tlistbox_5{padding:5px 10px;background:#fcfcfc;border-radius:5px;}
	    .tlistbox_6{width:100%;height:60px;}
	    .tlistbox_7{width:33.33%;height:60px;}
	    .tlistbox_8{width:100%;height:30px;line-height:30px;}
	    .tlistbox_9{width:100%;height:30px;line-height:20px;}
	    .tl{text-align:left;}
	    .tr{text-align:right;}
	    .tc{text-align:center;}
	    .fl{float:left;}
	    .fr{float:right;}
	    .red{color:#f5465c;}
        .bghc{background:#f5f5f5;}
        .green{color:#0ecb81;}
        .emptybox{width:100%;height:80px;line-height:80px;text-align:center;}
		body{
			min-height: 100vh;
		}
	</style>
</head>
<body >
	<div class="container-fluid " style="padding:0px;width:100vw;">
        <div class="no_header">
			<div class="fl allhg txtl">
				<i class="bi bi-arrow-left fcc fw" onclick="goback()" style="font-size: 24px;"></i>
			</div>
			<div class="fr allhg txtr" style="line-height:50px;width:10%;"></div>
		</div>
		<div class="contentbox">
		    <div class="contentbox_top" style="position: fixed;top: 50px;background:#1b1d29;">
		        <div class="titlebox">
		            <span class="xztitile fe6im" id="wtlistbtn">{:L('全部委托')}</span>
		        </div>
		        <div class="titlebox">
		            <span class="notitile fe6im" id="alllistbtn">{:L('历史记录')}</span>
		        </div>
		        <div class="titlebox">
		            <span class="notitile fe6im" id="finishbtn">{:L('成交明细')}</span>
		        </div>
		    </div>
		    
		    <!---成交记录-->
		    <div class="listbox" id="finishbox" style="display:none;">
		        <empty name="finishlist">
		        <div class="emptybox">
		            <span class="fcc fzmm">{:L('暂时没有记录')}</span>
		        </div>
		        <else />
		        <foreach name="finishlist" item="vo">
                <div class="tlistbox">
		            <div class="tlistbox_1">
		                <div class="tlistbox_2">
		                    <div class="tlistbox_3 fl">
		                        <if condition="$vo.type eq 1">
		                        <span class="fzmmm green">{:L('购买')}</span>
		                        <elseif condition="$vo.type eq 2" />
		                        <span class="fzmmm red">{:L('出售')}</span>    
		                        </if>
		                        <span class="fzmmm fe6im" style="font-weight:bold;">{$vo.symbol}</span>
		                    </div>
		                    
		                    <a href="{:U('Trade/billinfo')}?id={$vo.id}">
		                    <div class="tlistbox_4 fr tr">
		                        <span class="fcc fzm">{:L('已完成')}</span>
		                        <i class="bi bi-chevron-right"></i>
		                    </div>
		                    </a>
		                    
		                </div>
		                
		                <div class="tlistbox_6">
		                    <div class="tlistbox_7 fl">
		                        <div class="tlistbox_8 tl">
		                            <span class="fzm fcc">{:L('时间')}</span>
		                        </div>
		                        <div class="tlistbox_9 tl">
		                            <span class="fzmm  fc6b7785"><?php echo date("m-d H:i",strtotime($vo['tradetime']));?></span>
		                        </div>
		                    </div>
		                    <if condition="$vo.type eq 1">
		                    <div class="tlistbox_7 fl">
		                        <div class="tlistbox_8 tc">
		                            <span class="fzm fcc">{:L('委托价')}(USDT)</span>
		                        </div>
		                        <div class="tlistbox_9 tc">
		                            <span class="fzmm  fc6b7785">{$vo.xjprice}</span>
		                        </div>
		                    </div>
		                    <elseif condition="$vo.type eq 2" />
		                    <div class="tlistbox_7 fl">
		                        <div class="tlistbox_8 tc">
		                            <span class="fzm fcc">{:L('委托价')}(USDT)</span>
		                        </div>
		                        <div class="tlistbox_9 tc">
		                            <span class="fzmm  fc6b7785">{:L('市价')}</span>
		                        </div>
		                    </div>
		                    </if>


		                    <div class="tlistbox_7 fl">
		                        <div class="tlistbox_8 tr">
		                            <span class="fzm fcc">{:L('交易额')}(USDT)</span>
		                        </div>
		                        <div class="tlistbox_9 tr">
		                            <span class="fzmm  fc6b7785">{$vo.usdtnum}</span>
		                        </div>
		                    </div>
		                </div>

		                <div class="tlistbox_6">
		                    <div class="tlistbox_7 fl">
		                        <div class="tlistbox_8 tl">
		                            <span class="fzm fcc">{:L('成交总价')}(USDT)</span>
		                        </div>
		                        <div class="tlistbox_9 tl">
		                            <span class="fzmm  fc6b7785">{$vo.usdtnum}</span>
		                        </div>
		                    </div>
		                    <if condition="$vo.type eq 1">
		                    <div class="tlistbox_7 fl">
		                        <div class="tlistbox_8 tc">
		                            <span class="fzm fcc">{:L('成交均价')}(USDT)</span>
		                        </div>
		                        <div class="tlistbox_9 tc">
		                            <span class="fzmm  fc6b7785">{$vo.price}</span>
		                        </div>
		                    </div>
		                    <elseif condition="$vo.type eq 2" />
		                    <div class="tlistbox_7 fl">
		                        <div class="tlistbox_8 tc">
		                            <span class="fzm fcc">{:L('成交均价')}(USDT)</span>
		                        </div>
		                        <div class="tlistbox_9 tc">
		                            <span class="fzmm  fc6b7785">{$vo.price}</span>
		                        </div>
		                    </div>
		                    </if>

		                    <div class="tlistbox_7 fl">
		                        <div class="tlistbox_8 tr">
		                            <span class="fzm fcc">{:L('成交量')}({$vo.coin})</span>
		                        </div>
		                        <div class="tlistbox_9 tr">
		                            <span class="fzmm  fc6b7785">{$vo.coinnum}</span>
		                        </div>
		                    </div>
		                </div>

		            </div>
		        </div>
		        </foreach>
		        </empty>

		    </div>


		    <!---历史记录-->
		    <div class="listbox" id="alllistbox"  style="display:none;">
		        <empty name="alllist">
		        <div class="emptybox">
		            <span class="fcc fzmm">{:L('暂时没有记录')}</span>
		        </div>
		        <else />
		        <foreach name="alllist" item="vo">
                <div class="tlistbox">
		            <div class="tlistbox_1">
		                <div class="tlistbox_2">
		                    <div class="tlistbox_3 fl">
		                        <if condition="$vo.type eq 1">
		                        <span class="fzmmm green">{:L('购买')}</span>
		                        <elseif condition="$vo.type eq 2" />
		                        <span class="fzmmm red">{:L('出售')}</span>
		                        </if>
		                        <span class="fzmmm fe6im" style="font-weight:bold;">{$vo.symbol}</span>
		                    </div>

		                    <a href="{:U('Trade/billinfo')}?id={$vo.id}">
		                    <div class="tlistbox_4 fr tr">
		                        <if condition="$vo.status eq 1">
		                        <span class="fcc fzm">{:L('取消')}</span>
		                        <elseif  condition="$vo.status eq 2" />
		                        <span class="fcc fzm">{:L('已完成')}</span>
		                        <elseif  condition="$vo.status eq 3" />
		                        <span class="fcc fzm">{:L('已完成')}</span>
		                        </if>
		                        <i class="bi bi-chevron-right"></i>
		                    </div>
		                    </a>

		                </div>

		                <div class="tlistbox_6">
		                    <div class="tlistbox_7 fl">
		                        <div class="tlistbox_8 tl">
		                            <span class="fzm fcc">{:L('时间')}</span>
		                        </div>
		                        <div class="tlistbox_9 tl">
		                            <span class="fzmm  fc6b7785"><?php echo date("m-d H:i",strtotime($vo['tradetime']));?></span>
		                        </div>
		                    </div>
		                    <if condition="$vo.type eq 1">
		                    <div class="tlistbox_7 fl">
		                        <div class="tlistbox_8 tc">
		                            <span class="fzm fcc">{:L('委托价')}(USDT)</span>
		                        </div>
		                        <div class="tlistbox_9 tc">
		                            <span class="fzmm  fc6b7785">{$vo.xjprice}</span>
		                        </div>
		                    </div>
		                    <elseif condition="$vo.type eq 2" />
		                    <div class="tlistbox_7 fl">
		                        <div class="tlistbox_8 tc">
		                            <span class="fzm fcc">{:L('委托价')}(USDT)</span>
		                        </div>
		                        <div class="tlistbox_9 tc">
		                            <span class="fzmm  fc6b7785">{:L('市价')}</span>
		                        </div>
		                    </div>
		                    </if>


		                    <div class="tlistbox_7 fl">
		                        <div class="tlistbox_8 tr">
		                            <span class="fzm fcc">{:L('交易额')}(USDT)</span>
		                        </div>
		                        <div class="tlistbox_9 tr">
		                            <span class="fzmm  fc6b7785">{$vo.usdtnum}</span>
		                        </div>
		                    </div>
		                </div>

		                <div class="tlistbox_6">
		                    <div class="tlistbox_7 fl">
		                        <div class="tlistbox_8 tl">
		                            <span class="fzm fcc">{:L('成交总价')}(USDT)</span>
		                        </div>
		                        <div class="tlistbox_9 tl">
		                            <span class="fzmm  fc6b7785">{$vo.usdtnum}</span>
		                        </div>
		                    </div>
		                    <if condition="$vo.type eq 1">
		                    <div class="tlistbox_7 fl">
		                        <div class="tlistbox_8 tc">
		                            <span class="fzm fcc">{:L('成交均价')}(USDT)</span>
		                        </div>
		                        <div class="tlistbox_9 tc">
		                            <span class="fzmm  fc6b7785">{$vo.price}</span>
		                        </div>
		                    </div>
		                    <elseif condition="$vo.type eq 2" />
		                    <div class="tlistbox_7 fl">
		                        <div class="tlistbox_8 tc">
		                            <span class="fzm fcc">{:L('成交均价')}(USDT)</span>
		                        </div>
		                        <div class="tlistbox_9 tc">
		                            <span class="fzmm  fc6b7785">{$vo.price}</span>
		                        </div>
		                    </div>
		                    </if>

		                    <div class="tlistbox_7 fl">
		                        <div class="tlistbox_8 tr">
		                            <span class="fzm fcc">{:L('成交量')}({$vo.coin})</span>
		                        </div>
		                        <div class="tlistbox_9 tr">
		                            <span class="fzmm  fc6b7785">{$vo.coinnum}</span>
		                        </div>
		                    </div>
		                </div>


		            </div>
		        </div>
		        </foreach>
		        </empty>

		    </div>


		    <!----所有委托--->
		    <div class="listbox" id="wtlistbox" style="display:block;">
		        <empty name="list">
		        <div class="emptybox">
		            <span class="fcc fzmm">{:L('暂时没有记录')}</span>
		        </div>
		        <else />
		        <foreach name="list" item="vo">
                <div class="tlistbox">
		            <div class="tlistbox_1">
		                <div class="tlistbox_2">
		                    <div class="tlistbox_3 fl">
		                        <if condition="$vo.type eq 1">
		                        <span class="fzmmm green">{:L('购买')}</span>
		                        <elseif condition="$vo.type eq 2" />
		                        <span class="fzmmm red">{:L('出售')}</span>
		                        </if>
		                        <span class="fzmmm fe6im" style="font-weight:bold;">{$vo.symbol}</span>
		                    </div>

		                    <div class="tlistbox_4 fr tr"  onclick="clearorder({$vo.id});">
		                        <span class="tlistbox_5 fcy">{:L('取消')}</span>
		                    </div>
		                </div>


		                <div class="tlistbox_6">
		                    <div class="tlistbox_7 fl">
		                        <div class="tlistbox_8 tl">
		                            <span class="fzm fcc">{:L('价格')}(USDT)</span>
		                        </div>
		                        <div class="tlistbox_9 tl">
		                            <span class="fzmm  fc6b7785">{$vo.xjprice}</span>
		                        </div>
		                    </div>
		                    <if condition="$vo.type eq 1">
		                    <div class="tlistbox_7 fl">
		                        <div class="tlistbox_8 tc">
		                            <span class="fzm fcc">{:L('数量')}(USDT)</span>
		                        </div>
		                        <div class="tlistbox_9 tc">
		                            <span class="fzmm  fc6b7785">{$vo.usdtnum}</span>
		                        </div>
		                    </div>
		                    <elseif condition="$vo.type eq 2" />
		                    <div class="tlistbox_7 fl">
		                        <div class="tlistbox_8 tc">
		                            <span class="fzm fcc">{:L('数量')}({$vo.coin})</span>
		                        </div>
		                        <div class="tlistbox_9 tc">
		                            <span class="fzmm  fc6b7785">{$vo.coinnum}</span>
		                        </div>
		                    </div>
		                    </if>

		                    <div class="tlistbox_7 fl">
		                        <div class="tlistbox_8 tr">
		                            <span class="fzm fcc">{:L('实际成交')}({$vo.coin})</span>
		                        </div>
		                        <div class="tlistbox_9 tr">
		                            <span class="fzmm  fc6b7785">{$vo.coinnum}</span>
		                        </div>
		                    </div>
		                </div>
		                
		                
		            </div>
		        </div>
		        </foreach>
		        </empty>
		    </div>
		    
		    

		    <div style="width:100%;height:80px;"></div>
   
		</div>

	</div>	
</body>
<script src="https://cdn.staticfile.org/jquery/2.1.1/jquery.min.js"></script>
<script type="text/javascript" src="/Public/Static/js/layer/layer.js" ></script>
<script type="text/javascript">
    $("#wtlistbtn").click(function(){
        $("#wtlistbtn").addClass("xztitile");
        $("#wtlistbtn").removeClass("notitile");
        $("#alllistbtn").addClass("notitile");
        $("#alllistbtn").removeClass("xztitile");
        $("#finishbtn").addClass("notitile");
        $("#finishbtn").removeClass("xztitile");
        $("#wtlistbox").show();
        $("#alllistbox").hide();
        $("#finishbox").hide();
    });
    $("#alllistbtn").click(function(){
        $("#wtlistbtn").addClass("notitile");
        $("#wtlistbtn").removeClass("xztitile");
        $("#alllistbtn").addClass("xztitile");
        $("#alllistbtn").removeClass("notitile");
        $("#finishbtn").addClass("notitile");
        $("#finishbtn").removeClass("xztitile");
        $("#wtlistbox").hide();
        $("#alllistbox").show();
        $("#finishbox").hide();
    });
    $("#finishbtn").click(function(){
        $("#wtlistbtn").addClass("notitile");
        $("#wtlistbtn").removeClass("xztitile");
        $("#alllistbtn").addClass("notitile");
        $("#alllistbtn").removeClass("xztitile");
        $("#finishbtn").addClass("xztitile");
        $("#finishbtn").removeClass("notitile");
        $("#wtlistbox").hide();
        $("#alllistbox").hide();
        $("#finishbox").show();
    });
</script>
<script type="text/javascript">
    function clearorder(id){
        var oid = id;
        $.post("{:U('Trade/clearorder')}",
        {"oid":oid},
        function(data){
            if(data.code == 1){
                layer.msg(data.info);
                setTimeout(function(args){
                    window.location.reload();
                },2000);
            }else{
                layer.msg(data.info);return false;
            }    
        });
    }
</script>
<script type="text/javascript">
    function goback(){
        window.history.go(-1);
    }
</script>
</html>



